import { create } from 'zustand';

interface TimeSlot {
  startTime: string;
  endTime: string;
}

const useTimingStore = create((set) => ({
  // Store time slots as objects with startTime and endTime
  selectedTimeSlots: [] as TimeSlot[],
  
  // Toggle a time slot
  toggleSelectedTimeSlot: (time: string) => set((state) => {
    const timeSlot = {
      startTime: time,
      endTime: `${parseInt(time.split(':')[0]) + 1}:00`
    };
    
    const existingIndex = state.selectedTimeSlots.findIndex(
      slot => slot.startTime === timeSlot.startTime
    );
    
    if (existingIndex >= 0) {
      return { 
        selectedTimeSlots: state.selectedTimeSlots.filter(
          (_, index) => index !== existingIndex
        ) 
      };
    } else {
      return { 
        selectedTimeSlots: [...state.selectedTimeSlots, timeSlot] 
      };
    }
  }),
  
  // Clear all selected time slots
  clearSelectedTimeSlots: () => set({ selectedTimeSlots: [] })
}));

export default useTimingStore;