// store/areaStore.js
import { create } from 'zustand';

interface Area {
    _id: string;
    [key: string]: any; // Additional properties if any
}
interface AreaStore {
    selectedAreas: Area[];
    addArea: (area: Area) => void;
    removeArea: (areaId: string) => void;
    clearAreas: () => void;
}

const useAreaStore = create<AreaStore>((set) => ({
    selectedAreas: [],
    
    addArea: (area) => set((state) => {
        // Only add if not already in the array and limit to 5 areas
        if (state.selectedAreas.length < 5 && !state.selectedAreas.some(a => a._id === area._id)) {
            return { selectedAreas: [...state.selectedAreas, area] };
        }
        return state;
    }),
    
    removeArea: (areaId) => set((state) => ({
        selectedAreas: state.selectedAreas.filter(area => area._id !== areaId)
    })),
    
    clearAreas: () => set({ selectedAreas: [] }),
}));

export default useAreaStore;