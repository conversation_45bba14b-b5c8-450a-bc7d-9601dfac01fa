import { create } from 'zustand';

interface Sport {
  _id: string;
  name: string;
  description: string;
}

interface TrainerSportState {
  sports: string[]; // Array of sport IDs
  allSports: Sport[]; // All available sports from API
  toggleSport: (sport: Sport) => void;
  setAllSports: (sports: Sport[]) => void;
}

export const useTrainerStore = create<TrainerSportState>((set) => ({
  sports: [],
  allSports: [],
  toggleSport: (sport) => 
    set((state) => ({
      sports: state.sports.includes(sport._id)
        ? state.sports.filter(id => id !== sport._id)
        : [...state.sports, sport._id]
    })),
  setAllSports: (sports) => set({ allSports: sports }),
}));