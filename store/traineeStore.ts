import { create } from 'zustand';
import useSportStore from './sport';

interface Trainee {
  name: string;
  age: number;
  gender: string;
}

interface TraineeStore {
  trainees: Trainee[];
  duration: 1 | 3 | 6;
  totalAmount: number;
  
  // Actions
  addTrainee: (trainee: Trainee) => void;
  removeTrainee: (index: number) => void;
  setDuration: (duration: 1 | 3 | 6) => void;
  calculateTotalAmount: () => void;
  resetTrainees: () => void;
  updateTrainee: (index: number, updatedTrainee: Trainee) => void;
  
}

const useTraineeStore = create<TraineeStore>((set, get) => ({
  trainees: [],
  duration: 1, // Default duration
  totalAmount: 0,
  
  addTrainee: (trainee) => {
    set((state) => ({
      trainees: [...state.trainees, trainee]
    }));
    get().calculateTotalAmount();
  },
  
  removeTrainee: (index) => {
    set((state) => ({
      trainees: state.trainees.filter((_, i) => i !== index)
    }));
    get().calculateTotalAmount();
  },
  
   
  updateTrainee: (index, updatedTrainee) => 
    set((state) => {
      const newTrainees = [...state.trainees];
      newTrainees[index] = updatedTrainee;
      return { trainees: newTrainees };
    }),
  setDuration: (duration) => {
    set({ duration });
    get().calculateTotalAmount();
  },
  
  calculateTotalAmount: () => {
    const { trainees, duration } = get();
    const sport = useSportStore.getState().selectedSport;
    
    if (!sport) {
      set({ totalAmount: 0 });
      return;
    }
    
    let total = 0;
    
    trainees.forEach(trainee => {
      let category = '';
      if (trainee.age >= sport.ageCategories.children.minAge && 
          trainee.age <= sport.ageCategories.children.maxAge) {
        category = 'children';
      } else if (trainee.age >= sport.ageCategories.adults.minAge && 
                trainee.age <= sport.ageCategories.adults.maxAge) {
        category = 'adults';
      } else if (trainee.age >= sport.ageCategories.elderly.minAge && 
                trainee.age <= sport.ageCategories.elderly.maxAge) {
        category = 'elderly';
      }
      
      if (category) {
        // Calculate based on duration
        switch (duration) {
          case 1:
            total += sport.pricing[category].oneMonth;
            break;
          case 3:
            total += sport.pricing[category].threeMonths;
            break;
          case 6:
            total += sport.pricing[category].sixMonths;
            break;
          default:
            total += sport.pricing[category].oneMonth;
        }
      }
    });
    
    set({ totalAmount: total });
  },
  
  resetTrainees: () => {
    set({ trainees: [], totalAmount: 0 });
  }
}));

export default useTraineeStore;