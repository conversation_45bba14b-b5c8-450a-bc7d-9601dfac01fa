// store/trainerTimings.ts
import { create } from 'zustand';

type TimeSlot = {
  id: string;
  day: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
};

type TrainerTimingsStore = {
  timeSlots: TimeSlot[];
  toggleSlot: (slotId: string) => void;
  resetSlots: () => void;
  updateSlotTime: (slotId: string, type: 'start' | 'end', time: string) => void;
};

// Generate initial time slots for each day of the week
const generateInitialSlots = (): TimeSlot[] => {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  return days.map(day => ({
    id: `${day.toLowerCase()}-morning`,
    day,
    startTime: '08:00',
    endTime: '10:00',
    isAvailable: false
  }));
};

export const useTrainerTimingsStore = create<TrainerTimingsStore>((set) => ({
  timeSlots: generateInitialSlots(),
  toggleSlot: (slotId) => 
    set((state) => ({
      timeSlots: state.timeSlots.map(slot => 
        slot.id === slotId 
          ? { ...slot, isAvailable: !slot.isAvailable } 
          : slot
      )
    })),
  resetSlots: () => set({ timeSlots: generateInitialSlots() }),
  updateSlotTime: (slotId, type, time) =>
    set((state) => ({
      timeSlots: state.timeSlots.map(slot =>
        slot.id === slotId
          ? { ...slot, [type === 'start' ? 'startTime' : 'endTime']: time }
          : slot
      )
    })),
}));