# Trainer App - React Native Expo

A React Native training application migrated from React Native CLI to Expo.

## 🚀 Features

- **Firebase Authentication** - Phone number authentication with OTP verification
- **React Query** - Data fetching and state management
- **React Navigation** - Stack and tab navigation
- **Linear Gradients** - Beautiful UI with gradient backgrounds
- **Flash Messages** - Toast notifications
- **AsyncStorage** - Local data persistence
- **Expo managed workflow** - Easy development and deployment

## 📋 Prerequisites

- Node.js (16 or higher)
- npm or yarn
- Expo CLI: `npm install -g @expo/cli`
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd trainer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **iOS Setup**
   ```bash
   cd ios && pod install && cd ..
   ```

## 🔧 Firebase Configuration

**Important**: You need to configure Firebase before running the app.

1. **Create a Firebase project** at [Firebase Console](https://console.firebase.google.com/)

2. **Enable Authentication** with Phone Number sign-in method

3. **Add iOS app to your Firebase project**
   - Bundle ID: `com.stepup.trainer`
   - Download `GoogleService-Info.plist`
   - Place it in `ios/trainer/` directory

4. **Add Android app to your Firebase project** (if needed)
   - Package name: `com.stepup.trainer`
   - Download `google-services.json`
   - Place it in `android/app/` directory

5. **Update Firebase configuration**
   - Edit `firebase.config.js` with your actual Firebase config values
   - Replace placeholder values with your project's configuration

## 🏃‍♂️ Running the App

### Development Build

```bash
# iOS
npx expo run:ios

# Android
npx expo run:android

# Web
npx expo start --web
```

### Expo Go (Limited Firebase support)

```bash
npx expo start
```

**Note**: Firebase requires development builds, so Expo Go won't work with all features.

## 📱 App Structure

```
trainer/
├── app/                    # Expo Router app directory
│   └── _layout.tsx        # Root layout with providers
├── screens/               # App screens
│   ├── Auth.tsx          # Authentication screen
│   ├── Home.tsx          # Home screen
│   └── ...
├── navigation/           # Navigation configuration
│   ├── Navigation.tsx    # Stack navigator
│   └── Tabs.tsx         # Tab navigator
├── services/            # API and hooks
│   └── hooks/           # React Query hooks
├── components/          # Reusable components
├── styles/             # Styling
│   ├── color.ts        # Color definitions
│   └── font.ts         # Font definitions
├── assets/             # Images and icons
├── utils/              # Utility functions
├── store/              # State management
└── ios/                # iOS native files
```

## 🔑 Key Changes from React Native CLI

1. **Root Layout**: Moved from `App.tsx` to `app/_layout.tsx`
2. **Navigation**: Wrapped in NavigationContainer with `independent={true}`
3. **Firebase**: Added modular headers support in Podfile
4. **Dependencies**: All properly configured for Expo
5. **TypeScript**: Added proper types for better development experience

## 🎯 Main Screens

- **Selector**: Choose between trainee and trainer roles
- **Auth**: Phone authentication with OTP
- **Home**: Main dashboard after login
- **Trainer Screens**: Special screens for trainer users
- **More**: Settings and additional options

## 🔐 Authentication Flow

1. User selects role (Trainee/Trainer)
2. Enters phone number
3. Receives OTP via SMS
4. Verifies OTP
5. Automatic login with Firebase Auth
6. Role-based navigation to appropriate screens

## 🚨 Troubleshooting

### Firebase Issues
- Ensure `GoogleService-Info.plist` is in the correct iOS directory
- Verify Firebase project configuration
- Check that Phone Authentication is enabled in Firebase Console

### Pod Install Issues
- Run `cd ios && pod install --repo-update`
- Clear derived data if needed

### Build Issues
- Clear Metro cache: `npx expo start --clear`
- Clean iOS build: `npx expo run:ios --clear`

## 📦 Dependencies

### Core Dependencies
- `expo` - Expo SDK
- `react-native` - React Native framework
- `@react-navigation/native` - Navigation
- `@react-navigation/stack` - Stack navigation
- `@tanstack/react-query` - Data fetching
- `@react-native-firebase/app` - Firebase core
- `@react-native-firebase/auth` - Firebase authentication

### UI Dependencies
- `react-native-linear-gradient` - Gradient backgrounds
- `lucide-react-native` - Icons
- `react-native-flash-message` - Toast messages

### Storage
- `@react-native-async-storage/async-storage` - Local storage

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or need help with the setup, please:
1. Check the troubleshooting section above
2. Review the Expo and Firebase documentation
3. Open an issue in this repository
