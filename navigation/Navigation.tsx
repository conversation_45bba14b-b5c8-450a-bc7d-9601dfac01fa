import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { createStackNavigator,  CardStyleInterpolators
} from '@react-navigation/stack';
import Auth from '../screens/Auth';
import Selector from '../screens/Selector';
import Auth<PERSON>hecker from '../components/shared/AuthChecker';
import Onboarding from '../screens/Onboarding';
import Timings from '../screens/Timings';
import BookingConfirmation from '../screens/ChildSelection';
import SportDetails from '../screens/SportDetails';
import ChildSelection from '../screens/ChildSelection';
import Success from '../screens/Success';
import Qr from '../screens/Qr';
import TrainerHomes from '../screens/trainer/TrainerHomes';
import AddTrainer from '../screens/trainer/AddTrainer';
import TrainersTimings from '../screens/trainer/TrainersTimings';
import FinalScreen from '../screens/trainer/FinalScreen';
import AllSet from '../screens/trainer/AllSet';
import TrainerFeed from '../screens/trainer/TrainerFeed';
import FAQ from '../components/FAQ';
import PrivacyPolicy from '../components/PrivacyPolicy';
import More from '../screens/tabs/More';
import Carousel from '../screens/Carousel';
import Areas from '../screens/trainer/Areas';


const Stack = createStackNavigator();

const Navigation = () => {
  return (
   
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="checker" component={AuthChecker} />
    <Stack.Screen name="selector" component={Selector} options={{
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOSInverted
    }}/>
    <Stack.Screen name="onboarding" component={Onboarding} options={{
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOSInverted
    }}/>
       <Stack.Screen name="carousel" component={Carousel} options={{
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOSInverted
    }}/>
    <Stack.Screen name="auth" component={Auth} options={{
       cardStyleInterpolator: CardStyleInterpolators.forNoAnimation,
    }}/>
    <Stack.Screen name="timings" component={Timings} />
    <Stack.Screen name="information" component={SportDetails} />
    <Stack.Screen name="success" component={Success} />
    <Stack.Screen name="select_child" component={ChildSelection} />
    <Stack.Screen name="qr" component={Qr} />
    <Stack.Screen name="more" component={More} />
    {/* more */}
    <Stack.Screen name="faq" component={FAQ} />
    <Stack.Screen name="privacy_policy" component={PrivacyPolicy} />
    {/* tariner */}
    <Stack.Screen name="trainer_home" component={TrainerHomes} />
    <Stack.Screen name="add_trainer" component={AddTrainer} />
    <Stack.Screen name="trainer_timings" component={TrainersTimings} />
    <Stack.Screen name="trainer_final_screen" component={FinalScreen} />
    <Stack.Screen name="trainer_all_set" component={AllSet} />
    <Stack.Screen name="trainer_feed" component={TrainerFeed} />
    <Stack.Screen name="trainer_areas" component={Areas} />
  </Stack.Navigator>
  )
}

export default Navigation

const styles = StyleSheet.create({})