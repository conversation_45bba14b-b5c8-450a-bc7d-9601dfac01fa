import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React from "react";
import FlashMessage from "react-native-flash-message";
import "react-native-reanimated";

import { useColorScheme } from "@/hooks/useColorScheme";
import Carousel from "@/screens/Carousel";

const queryClient = new QueryClient();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  if (!loaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="checker" />
          <Stack.Screen
            name="selector"
          />
          <Stack.Screen
            name="onboarding"
          />
          <Stack.Screen
            name="carousel"
          />
          <Stack.Screen
            name="auth"
          />
          <Stack.Screen name="tabs" />
          <Stack.Screen name="timings" />
          <Stack.Screen name="information" />
          <Stack.Screen name="success" />
          <Stack.Screen name="select_child" />
          <Stack.Screen name="qr" />
          <Stack.Screen name="more" />
          {/* more */}
          <Stack.Screen name="faq" />
          <Stack.Screen name="privacy_policy" />
          {/* tariner */}
          <Stack.Screen name="trainer_home" />
          <Stack.Screen name="add_trainer" />
          <Stack.Screen name="trainer_timings" />
          <Stack.Screen name="trainer_final_screen" />
          <Stack.Screen name="trainer_all_set" />
          <Stack.Screen name="trainer_feed" />
          <Stack.Screen name="trainer_areas" />
        </Stack>
        <FlashMessage position="top" />
        <StatusBar style="auto" />
      </ThemeProvider>
    </QueryClientProvider>
  );
}
