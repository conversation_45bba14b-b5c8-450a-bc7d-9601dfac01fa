import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  Image, 
  StyleSheet, 
  Dimensions 
} from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react-native';
import { assets } from '../assets/assets';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import LinearGradient from 'react-native-linear-gradient';
import { useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

const Selector = () => {
  const router = useRouter();
  return (
    <LinearGradient 
    colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
    style={styles.container}>
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)",Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
        />

      <View style={styles.contentContainer}>
        <View style={styles.logoContainer}>
          <Image 
            source={assets.logoLime} 
            style={styles.logo} 
            resizeMode="contain"
          />
        </View>

        <Text style={styles.welcomeText}>Welcome</Text>
        <Text style={styles.subtitleText}>Select your role to continue</Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.roleButton, styles.parentButton]}
            onPress={() => router.push('/auth')}
          >
            <View style={styles.iconContainer}>
              <Users color={Colors.lime} size={40} />
            </View>
            <Text style={styles.buttonText}>Parent</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.roleButton, styles.trainerButton]}
            onPress={() => {/* Handle trainer role selection */}}
          >
            <View style={styles.iconContainer}>
              <Dumbbell color={Colors.white} size={40} />
            </View>
            <Text style={[styles.buttonText,{color:Colors.white}]}>Trainer</Text>
          </TouchableOpacity>
        </View>


        <Text style={styles.journeyText}>Your sports journey begins here!</Text>
        
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.darkBg,

  },
 
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    marginTop:100,
    paddingHorizontal: 20,
    zIndex: 10, // Ensure content is above the background
  },
  logoContainer: {
    marginBottom: 30,
  },
  logo: {
    width: 100,
    height: 100,
  },
  welcomeText: {
    fontSize: 30,

    fontFamily:Fonts.iMedium ,
    color: Colors.white,
    marginBottom: 40,
  },
  subtitleText: {
    fontSize: 16,
    fontFamily:Fonts.iRegular ,
    color: Colors.lightGray,
    marginBottom: 30,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 30,
  },
  roleButton: {
    width: width * 0.4,
    height: width * 0.45,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  parentButton: {
    backgroundColor: Colors.darkBg, // Light green
    borderWidth: 1.5,
    borderColor: Colors.lime
  },
  trainerButton: {
    backgroundColor: Colors.darkBg, // Light gray
  },
  iconContainer: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonText: {
    fontSize: 20,
    fontFamily:Fonts.iRegular ,
    color: Colors.lime,
  },
  journeyText: {
    fontSize: 16,
    color: Colors.lightGray,
    fontFamily:Fonts.iLight ,
  },
  gradient:{
    opacity:0.1,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: height * 0.3
  }
});

export default Selector;