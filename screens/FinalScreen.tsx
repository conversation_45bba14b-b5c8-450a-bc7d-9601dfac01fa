import { StyleSheet, Text, View, ScrollView, TextInput, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import React, { useState } from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { ChevronLeft, Check, Clock, Activity, MapPin } from 'lucide-react-native';
import { useTrainerStore } from '../store/trainerSport';
import useTimingStore from '../store/time';
import { addTrainerPreferences } from '../services/api';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import useAreaStore from '../store/area';
import { useRouter } from 'expo-router';

const FinalScreen = () => {
  const router = useRouter();
  const { sports, allSports } = useTrainerStore();
  const { selectedTimeSlots } = useTimingStore() as any;
  const { selectedAreas } = useAreaStore();
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);

  const getSportName = (id: any) => {
    const sport = allSports.find((s: any) => s._id === id);
    return sport ? sport.name : 'Unknown Sport';
  };

  const handleSubmit = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter your name');
      return;
    }

    if (sports.length === 0) {
      Alert.alert('Error', 'Please select at least one sport');
      return;
    }

    if (selectedTimeSlots.length === 0) {
      Alert.alert('Error', 'Please select at least one time slot');
      return;
    }

    if (selectedAreas.length === 0) {
      Alert.alert('Error', 'Please select at least one area');
      return;
    }

    try {
      setLoading(true);
      // Extract area IDs for the API
      const areaIds = selectedAreas.map((area: any) => area._id);
      await addTrainerPreferences(name.trim(), sports, selectedTimeSlots, areaIds);
      setLoading(false);
      router.replace('/trainer_all_set');
    } catch (error) {
      setLoading(false);
      Alert.alert('Error', 'Failed to save preferences. Please try again.');
      console.error('Error saving preferences:', error);
    }
  };

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}
    >
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)",Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <ChevronLeft color={Colors.lime} size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Trainer Preferences</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Name Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Your Name</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            placeholder="Enter your name"
            placeholderTextColor={Colors.gray}
          />
        </View>

        {/* Selected Sports */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Selected Sports</Text>
          {sports.length > 0 ? (
            sports.map((sportId, index) => (
              <View key={index} style={styles.itemCard}>
                <View style={styles.itemIcon}>
                  <Activity color={Colors.lime} size={18} />
                </View>
                <Text style={styles.itemText}>{getSportName(sportId)}</Text>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No sports selected</Text>
          )}
        </View>

        {/* Selected Time Slots */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Selected Time Slots</Text>
          {selectedTimeSlots.length > 0 ? (
            selectedTimeSlots.map((slot: any, index: any) => (
              <View key={index} style={styles.itemCard}>
                <View style={styles.itemIcon}>
                  <Clock color={Colors.lime} size={18} />
                </View>
                <Text style={styles.itemText}>
                  {slot.startTime} - {slot.endTime}
                </Text>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No time slots selected</Text>
          )}
        </View>

        {/* Selected Areas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Selected Areas</Text>
          {selectedAreas.length > 0 ? (
            selectedAreas.map((area, index) => (
              <View key={index} style={styles.itemCard}>
                <View style={styles.itemIcon}>
                  <MapPin color={Colors.lime} size={18} />
                </View>
                <Text style={styles.itemText}>{area.name}</Text>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No areas selected</Text>
          )}
        </View>
      </ScrollView>

      {/* Submit Button */}
      <TouchableOpacity 
        style={styles.submitButton}
        onPress={handleSubmit}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color={Colors.black} />
        ) : (
          <>
            <Check color={Colors.black} size={20} />
            <Text style={styles.submitButtonText}>Save Preferences</Text>
          </>
        )}
      </TouchableOpacity>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: '40%'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontFamily: Fonts.iMedium,
    fontSize: 18,
    color: Colors.white,
    marginBottom: 12,
  },
  itemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: Colors.lime,
  },
  itemIcon: {
    marginRight: 12,
  },
  itemText: {
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
  },
  emptyText: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.gray,
    fontStyle: 'italic',
  },
  submitButton: {
    backgroundColor: Colors.lime,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
  },
  submitButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
    marginLeft: 8,
  },
});

export default FinalScreen;