import React from 'react';
import { 
  Dimensions, 
  StyleSheet, 
  Text, 
  View, 
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  ScrollView
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { ChevronLeft, Clock, X } from 'lucide-react-native';
import useTimingStore from '../store/time';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import { useRouter } from 'expo-router';

const { height, width } = Dimensions.get('window');

const TrainerTimings = () => {
  const router = useRouter();
  console.log("screens trainers timings....");
  // Generate time slots from 06:00 to 22:00 in one-hour intervals
  const generateTimeSlots = () => {
    const timeSlots = [];
    for (let hour = 6; hour <= 22; hour++) {
      const formattedHour = hour.toString().padStart(2, '0');
      timeSlots.push(`${formattedHour}:00`);
    }
    return timeSlots;
  };

  const timeSlots = generateTimeSlots();
  const { selectedTimeSlots, toggleSelectedTimeSlot } = useTimingStore() as any;

  const handleTimeSelect = (time: string) => {
    toggleSelectedTimeSlot(time);
  };

  const handleProceed = () => {
    if (selectedTimeSlots.length > 0) {
      router.push('/trainer_final_screen'); // Replace with your actual next screen
    }
  };

  const renderTimeSlot = ({ item: time }: {item: any}) => {
    const isSelected = selectedTimeSlots.some(
      (slot: any) => slot.startTime === time
    );
    
    return (
      <TouchableOpacity 
        style={[
          styles.timeSlotCard, 
          isSelected && styles.selectedTimeSlotCard
        ]} 
        onPress={() => handleTimeSelect(time)}
      >
        <Clock 
          color={isSelected ? Colors.black : Colors.white} 
          size={20} 
          style={styles.clockIcon} 
        />
        <Text style={[
          styles.timeSlotText, 
          isSelected && styles.selectedTimeSlotText
        ]}>
          {time}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}  
    >
      <SafeAreaView style={styles.container}>
        <LinearGradient 
          colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)",Colors.lime]}
          style={styles.gradient} 
          start={{x: 0.5, y: 0}}
          end={{x: 0.5, y: 1}}
        />
        
        <View style={styles.header}>
          <TouchableOpacity 
            onPress={() => router.back()} 
            style={styles.backButton}
          >
            <ChevronLeft color={Colors.lime} size={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Select Timing</Text>
        </View>

        <ScrollView 
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContentContainer}
        >
          <View style={styles.contentContainer}>
            <Text style={styles.sectionTitle}>Available Slots</Text>
            <Text style={styles.sectionSubtitle}>Choose your preferred time slots</Text>

            {/* Display selected times */}
            {selectedTimeSlots.length > 0 && (
              <View style={styles.selectedTimesContainer}>
                <Text style={styles.selectedTimesTitle}>Selected Times:</Text>
                <View style={styles.selectedTimesChips}>
                    {selectedTimeSlots.sort((a: any, b: any) => a.startTime.localeCompare(b.startTime)).map((slot: any) => (
                    <TouchableOpacity 
                      key={slot.startTime} 
                      style={styles.selectedTimeChip}
                      onPress={() => toggleSelectedTimeSlot(slot.startTime)}
                    >
                      <Text style={styles.selectedTimeChipText}>
                        {slot.startTime} - {slot.endTime}
                      </Text>
                      <X color={Colors.black} size={14} style={styles.removeIcon} />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            <FlatList
              data={timeSlots}
              renderItem={renderTimeSlot}
              keyExtractor={(item) => item}
              numColumns={3}
              columnWrapperStyle={styles.gridRow}
              contentContainerStyle={styles.gridContainer}
              scrollEnabled={false} // Disable scrolling for the FlatList since we're using ScrollView
            />
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity 
            style={[
              styles.proceedButton, 
              selectedTimeSlots.length === 0 && styles.disabledButton
            ]}
            onPress={handleProceed}
            disabled={selectedTimeSlots.length === 0}
          >
            <Text style={styles.proceedButtonText}>
              {selectedTimeSlots.length > 0 
                ? `Proceed with ${selectedTimeSlots.length} ${selectedTimeSlots.length === 1 ? 'slot' : 'slots'}`
                : 'Select at least one time'
              }
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 100, // Add padding to accommodate the fixed footer
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    marginBottom: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.lightGray,
    marginTop: 4,
    fontFamily: Fonts.iLight,
    marginBottom: 16,
  },
  selectedTimesContainer: {
    marginBottom: 16,
  },
  selectedTimesTitle: {
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
    marginBottom: 8,
  },
  selectedTimesChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selectedTimeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.lime,
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    margin: 4,
  },
  selectedTimeChipText: {
    color: Colors.black,
    fontFamily: Fonts.iMedium,
    fontSize: 14,
    marginRight: 4,
  },
  removeIcon: {
    marginLeft: 4,
  },
  gridContainer: {
    // Add some bottom padding to prevent content being hidden behind proceed button
    paddingBottom: 16,
  },
  gridRow: {
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  timeSlotCard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.white,
    borderRadius: 8,
    padding: 12,
    width: (width - 64) / 3, // 3 columns with more padding
    justifyContent: 'center',
  },
  selectedTimeSlotCard: {
    borderColor: Colors.white,
    borderWidth: 1.5,
    backgroundColor: Colors.lime,
  },
  timeSlotText: {
    fontSize: 14,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
    marginLeft: 8,
  },
  selectedTimeSlotText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
  },
  clockIcon: {
    marginRight: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingBottom: 24,
    backgroundColor: 'transparent',
  },
  proceedButton: {
    flexDirection: 'row',
    backgroundColor: Colors.lime,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  disabledButton: {
    backgroundColor: "#E5E5E5",
  },
  proceedButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontFamily: Fonts.iSemiBold,
  },
  gradient: { 
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
    opacity: 0.4,
    height: height * 0.4,
  },
});

export default TrainerTimings;