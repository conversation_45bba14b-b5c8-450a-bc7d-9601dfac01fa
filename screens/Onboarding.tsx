import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  Image,
  FlatList,
  Modal,
} from 'react-native';
import React, { useState } from 'react';
import { Fonts } from '../styles/font';
import { Colors } from '../styles/color';
import { MapPin, ChevronDown, Check } from 'lucide-react-native';
import LinearGradient from 'react-native-linear-gradient';
import useGetSocieties from '../services/hooks/society/useGetSocieties';
import useAddParent from '../services/hooks/parent/useAddParent';
import { assets } from '../assets/assets';
import useSocietyStore from '../store/society';
import { useRouter } from 'expo-router';

const { height } = Dimensions.get('window');

const Onboarding = () => {
  const router = useRouter();
  const [selectedSociety, setSelectedSociety] = useState<any>(null);
  const [showSocietyDropdown, setShowSocietyDropdown] = useState(false);
  
  const { data: societiesData, isLoading: isLoadingSocieties } = useGetSocieties();
  const { mutate: addParent, isPending: isSubmitting } = useAddParent();
  const setSociety = useSocietyStore((state: any) => state.setSociety);

  const handleSubmit = () => {
    if (!selectedSociety) return;
    
    // Save society to Zustand store
    setSociety(selectedSociety);
    
    // Call API to add parent
    addParent({
      societyId: selectedSociety._id
    });
    
   
  };

  const isFormValid = selectedSociety;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="rgba(0, 0, 0, 0.9)" />
       <LinearGradient
                    colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)", Colors.lime]}
                    style={styles.gradient} 
                    start={{x: 0.5, y: 0}}
                    end={{x: 0.5, y: 1}}
                    />
      
      <LinearGradient
                   colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)", Colors.lime]}
                    style={styles.topGradient} 
                    end={{x: 0.5, y: 0}}
                    start={{x: 0.5, y: 1}}
                    />
      <View style={styles.contentContainer}>
        {/* Logo and App Name */}
        <View style={styles.logoContainer}>
          <Image 
            source={assets.icon} 
            style={styles.logo} 
            resizeMode="contain"
          />
       
        </View>
        
        {/* Society Selection Form */}
        <View style={styles.formSection}>
          <Text style={styles.formLabel}>Select your society name</Text>
          
          <TouchableOpacity 
            style={styles.societySelector} 
            onPress={() => setShowSocietyDropdown(true)}
          >
            <View style={styles.selectorContent}>
              <MapPin color={Colors.white} size={24} />
              <Text style={[styles.societyText, !selectedSociety && styles.placeholderText]}>
                {selectedSociety ? selectedSociety.name : 'Select Your Society'}
              </Text>
            </View>
            <ChevronDown color={Colors.white} size={24} />
          </TouchableOpacity>
          
          {/* Complete Registration Button */}
          <TouchableOpacity
            style={[styles.registerButton, !isFormValid && styles.disabledButton]}
            onPress={handleSubmit}
            disabled={!isFormValid || isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator color={Colors.black} size="small" />
            ) : (
              <Text style={styles.registerButtonText}>Complete Registration</Text>
            )}
          </TouchableOpacity>
        </View>
        
        {/* Why Step-Up Section */}
        <View style={styles.whySection}>
          <Text style={styles.whyTitle}>Why Step-Up ?</Text>
          
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitText}>• Get Trained in " <Text style={[{color: Colors.lime}]}>{selectedSociety?.name || '______________'}"</Text></Text>
            </View>
            
            <View style={styles.benefitItem}>
              <Text style={styles.benefitText}>• Ease of choosing from Highly Rated Trainers</Text>
            </View>
            
            <View style={styles.benefitItem}>
              <Text style={styles.benefitText}>• Notifications & Progress Tracker</Text>
            </View>
            
            <View style={styles.benefitItem}>
              <Text style={styles.benefitText}>• Structure & Exposure of sport with right Trainer</Text>
            </View>
          </View>
        </View>
        
        {/* Terms Footer */}
        <Text style={styles.termsText}>
          By registering you agree to <TouchableOpacity onPress={()=> {router.push("/privacy_policy")}}><Text style={{color: Colors.lime, fontFamily:Fonts.iMedium}}>Terms of Service and Privacy</Text></TouchableOpacity>
        </Text>
      </View>

      {/* Society Selection Modal */}
      <Modal
        visible={showSocietyDropdown}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSocietyDropdown(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Your Society</Text>
              <TouchableOpacity onPress={() => setShowSocietyDropdown(false)}>
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>
            
            {isLoadingSocieties ? (
              <ActivityIndicator size="large" color={Colors.lime} style={styles.modalLoader} />
            ) : (
              <FlatList
                data={societiesData?.data || []}
                keyExtractor={(item) => item._id}
                renderItem={({ item }) => (
                  <TouchableOpacity 
                    style={styles.societyItem}
                    onPress={() => {
                      setSelectedSociety(item);
                      setShowSocietyDropdown(false);
                    }}
                  >
                    <View style={styles.societyItemContent}>
                      <View>
                        <Text style={styles.societyName}>{item.name}</Text>
                        <Text style={styles.societyAddress}>{item.address}, {item.city}</Text>
                      </View>
                      {selectedSociety && selectedSociety._id === item._id && (
                        <Check color={Colors.lime} size={20} />
                      )}
                    </View>
                  </TouchableOpacity>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                ListEmptyComponent={() => (
                  <Text style={styles.emptyListText}>No societies available</Text>
                )}
              />
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default Onboarding;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
    justifyContent: 'space-between',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 10,
  },
  logo: {
    width: 200,
    height: 200,
  },
  appName: {
    fontSize: 34,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginTop: 8,
  },
  highlightText: {
    color: Colors.lime,
  },
  tagline: {
    fontSize: 14,
    fontFamily: Fonts.iRegular,
    color: Colors.white,
    marginTop: 5,
  },
  formSection: {
    width: '100%',
    marginBottom: 40,
  },
  formLabel: {
    fontSize: 20,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 20,
  },
  societySelector: {
    backgroundColor: 'rgba(50, 50, 50, 0.6)',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  societyText: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.white,
    marginLeft: 12,
    flex: 1,
  },
  placeholderText: {
    color: Colors.gray,
  },
  registerButton: {
    backgroundColor: Colors.lime,
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    backgroundColor: 'rgba(100, 100, 100, 0.5)',
  },
  registerButtonText: {
    color: Colors.black,
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
  },
  whySection: {
    marginBottom: 40,
  },
  whyTitle: {
    fontSize: 24,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 24,
    textAlign: 'center',
  },
  benefitsList: {
    marginBottom: 16,
  },
  benefitItem: {
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.white,
    lineHeight: 24,
  },
  termsText: {
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.iRegular,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'rgba(30, 30, 30, 0.95)',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 24,
    paddingBottom: 40,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
  },
  closeButton: {
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.lime,
  },
  modalLoader: {
    marginVertical: 40,
  },
  societyItem: {
    paddingVertical: 16,
  },
  societyItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  societyName: {
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
    marginBottom: 4,
  },
  societyAddress: {
    fontSize: 14,
    fontFamily: Fonts.iRegular,
    color: Colors.gray,
  },
  separator: {
    height: 1,
    backgroundColor: '#333',
  },
  emptyListText: {
    textAlign: 'center',
    padding: 20,
    fontFamily: Fonts.iRegular,
    color: Colors.gray,
  },
  gradient:{
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex:0,
    opacity:0.1,
    height: height * 0.4
  },
  topGradient:{
    position: "absolute",
    left: 0,
    opacity:0.1,
    right: 0,
    top: 0,
    zIndex:0,
 
    height: height * 0.4
  }
});