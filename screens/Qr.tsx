import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  SafeAreaView, 
  TouchableOpacity, 
  Dimensions,
  Share,
  Platform,
  StatusBar
} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { ChevronLeft, Download, Share2 } from 'lucide-react-native';
import LinearGradient from 'react-native-linear-gradient';
import ViewShot from 'react-native-view-shot';

import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import { useLocalSearchParams, useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

const Qr = () => {
  // Assuming you'll pass some user/booking data via route params
  const { userId, bookingId } = useLocalSearchParams();
  const router = useRouter();
  const qrData = JSON.stringify({ userId, bookingId });

  const [qrRef, setQrRef] = useState<ViewShot | null>(null);

  const handleShare = async () => {
    try {
      // Capture QR code as an image
      const uri = await qrRef.capture() ;
      
      await Share.share({
        title: 'My Booking QR Code',
        message: 'Here is my booking QR code',
        url: Platform.OS === 'ios' ? uri : `file://${uri}`
      });
    } catch (error) {
      console.error('Error sharing QR code:', error);
    }
  };

  const handleDownload = async () => {
    try {
      const uri = await qrRef.capture();
      // Implement download logic based on platform
      console.log('QR Code saved at:', uri);
    } catch (error) {
      console.error('Error downloading QR code:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="rgba(0, 0, 0, 0.9)" />
      
      {/* Background gradients */}
      <LinearGradient 
        colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
        style={styles.backgroundGradient} 
      />
      
      <LinearGradient 
        colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)", Colors.lime]}
        style={styles.bottomGradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)", Colors.lime]}
        style={styles.topGradient} 
        end={{x: 0.5, y: 0}}
        start={{x: 0.5, y: 1}}
      />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => router.back()} 
          style={styles.backButton}
        >
          <ChevronLeft color={Colors.lime} size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Pay Now</Text>
      </View>

      <View style={styles.content}>
        <ViewShot 
          ref={(ref) => setQrRef(ref)}
          options={{ format: 'png', quality: 1.0 }}
        >
          <View style={styles.qrContainer}>
            <QRCode
              value={qrData}
              size={250}
              color={Colors.black}
              backgroundColor={Colors.white}
            />
            <Text style={styles.qrText}>Pay on this QR</Text>
          </View>
        </ViewShot>

        <View style={styles.actionContainer}>
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={handleDownload}
          >
            <Download color={Colors.lime} size={24} />
            <Text style={styles.actionText}>Download</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={handleShare}
          >
            <Share2 color={Colors.lime} size={24} />
            <Text style={styles.actionText}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  bottomGradient: { 
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  topGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    zIndex: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  qrContainer: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.lime,
  },
  qrText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.black,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 32,
    gap: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.darkBg,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
    borderWidth: 1,
    borderColor: Colors.lime,
  },
  actionText: {
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
});

export default Qr;