import { 
  StyleSheet, 
  Text, 
  View, 
  TouchableOpacity, 
  FlatList, 
  TextInput, 
  Alert,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import React, { useState, useRef, useEffect } from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { ChevronLeft, MapPin, Check, X } from 'lucide-react-native';
import { getAllAreas } from '../services/api';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import useAreaStore from '../store/area';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

const Areas = () => {
  const router = useRouter();
  const { selectedAreas, addArea, removeArea } = useAreaStore();
  const [searchText, setSearchText] = useState('');
  const flatListRef = useRef<any>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [areas, setAreas] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>();

  useEffect(() => {
    fetchAreas();
  }, []);

  const fetchAreas = async () => {
    try {
      setLoading(true);
      const response = await getAllAreas();
      if (response.success) {
        setAreas(response.data);
      } else {
        setError('Failed to load areas');
      }
    } catch (error) {
      console.error('Error fetching areas:', error);
      setError('Failed to load areas');
    } finally {
      setLoading(false);
    }
  };

  const filteredAreas = areas.filter(area => 
    area.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const isAreaSelected = (areaId:any) => {
    return selectedAreas.some((area:any) => area._id === areaId);
  };

  const handleAreaSelect = (area:any) => {
    if (isAreaSelected(area._id)) {
      removeArea(area._id);
    } else {
      if (selectedAreas.length >= 5) {
        Alert.alert('Limit Reached', 'You can select up to 5 areas only.');
        return;
      }
      addArea(area);
    }
  };

  const handleContinue = () => {
    if (selectedAreas.length === 0) {
      Alert.alert('Select Areas', 'Please select at least one area to continue.');
      return;
    }
    router.push('/trainer_home');
  };



  const renderAreaItem = ({ item }:any) => (
    <TouchableOpacity 
      style={[
        styles.areaItem, 
        isAreaSelected(item._id) && styles.areaItemSelected
      ]}
      onPress={() => handleAreaSelect(item)}
    >
      <MapPin 
        size={18} 
        color={isAreaSelected(item._id) ? Colors.black : Colors.lime} 
      />
      <Text 
        style={[
          styles.areaText, 
          isAreaSelected(item._id) && styles.areaTextSelected
        ]}
      >
        {item.name}
      </Text>
      {isAreaSelected(item._id) && (
        <View style={styles.checkIcon}>
          <Check size={14} color={Colors.black} />
        </View>
      )}
    </TouchableOpacity>
  );


  if (loading) {
    return (
      <LinearGradient 
        colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
        style={[styles.container, styles.centerContent]}
      >
        <ActivityIndicator size="large" color={Colors.lime} />
        <Text style={styles.loadingText}>Loading areas...</Text>
      </LinearGradient>
    );
  }

  if (error) {
    return (
      <LinearGradient 
        colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
        style={[styles.container, styles.centerContent]}
      >
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchAreas}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}
    >
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)",Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <ChevronLeft color={Colors.lime} size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Select Areas</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.subtitle}>
          Select up to 5 areas where you want to provide training
        </Text>

       
      

        {/* Search Input */}
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search areas..."
            placeholderTextColor={Colors.gray}
            value={searchText}
            onChangeText={setSearchText}
          />
        </View>

        {/* Available Areas List */}
        <View style={styles.areasListContainer}>
          <Text style={styles.sectionTitle}>Available Areas ({filteredAreas.length})</Text>
          {filteredAreas.length > 0 ? (
            <FlatList
              data={filteredAreas}
              renderItem={renderAreaItem}
              keyExtractor={(item) => item._id}
              showsVerticalScrollIndicator={false}
              numColumns={2}
              contentContainerStyle={styles.areasList}
            />
          ) : (
            <Text style={styles.emptyText}>
              {searchText ? "No areas match your search" : "No areas available"}
            </Text>
          )}
        </View>
      </View>

      {/* Continue Button */}
      <TouchableOpacity 
        style={styles.continueButton}
        onPress={handleContinue}
      >
        <Text style={styles.continueButtonText}>Continue</Text>
      </TouchableOpacity>
    </LinearGradient>
  );
};

export default Areas;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: '40%'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  subtitle: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.gray,
    marginBottom: 20,
  },
  carouselContainer: {
    marginBottom: 24,
  },
  carouselSlide: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  selectedAreaCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 24,
    width: '90%',
    borderLeftWidth: 3,
    borderLeftColor: Colors.lime,
    position: 'relative',
  },
  selectedAreaText: {
    fontFamily: Fonts.iMedium,
    fontSize: 18,
    color: Colors.white,
    marginLeft: 16,
  },
  removeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  paginationDotInactive: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  paginationDotActive: {
    backgroundColor: Colors.lime,
  },
  searchContainer: {
    marginBottom: 20,
  },
  searchInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
  },
  sectionTitle: {
    fontFamily: Fonts.iMedium,
    fontSize: 18,
    color: Colors.white,
    marginBottom: 12,
  },
  areasListContainer: {
    flex: 1,
  },
  areasList: {
    paddingBottom: 100,
  },
  areaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    padding: 12,
    margin: 4,
    flex: 1,
  },
  areaItemSelected: {
    backgroundColor: Colors.lime,
  },
  areaText: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
    marginLeft: 8,
    flex: 1,
  },
  areaTextSelected: {
    color: Colors.black,
  },
  checkIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  continueButton: {
    backgroundColor: Colors.lime,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
  },
  continueButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
  },
  loadingText: {
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
    marginTop: 12,
  },
  errorText: {
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: Colors.lime,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
    color: Colors.black,
  },
  emptyText: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.gray,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
  carouselContent: {
    paddingVertical: 10,
  }
});