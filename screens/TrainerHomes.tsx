import React, { useEffect, useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  SafeAreaView, 
  TouchableOpacity, 
  Image,
  ScrollView,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { 
  ChevronRight, 
  Users, 
  Clock,
  CircleUser,
  ArrowRight,
  ChevronLeft
} from 'lucide-react-native';
import { Fonts } from '../styles/font';
import { Colors } from '../styles/color';
import LinearGradient from 'react-native-linear-gradient';
import { assets } from '../assets/assets';
import { useTrainerStore } from '../store/trainerSport';
import { getAllSports } from '../services/api';
import { useRouter } from 'expo-router';

const {height} = Dimensions.get('window');

const TrainerHomes = () => {
  const router = useRouter();
  const { sports: selectedSports, toggleSport, allSports, setAllSports } = useTrainerStore();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchSports = async () => {
      try {
        setLoading(true);
        const response = await getAllSports();
        if (response.success) {
          setAllSports(response.data);
        } else {
          setError('Failed to fetch sports');
        }
      } catch (err) {
        setError('Network error. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchSports();
  }, []);

  const renderSportItem = (sport: any) => {
    const isSelected = selectedSports.includes(sport._id);
    
    return (
      <TouchableOpacity 
        key={sport._id} 
        style={[
          styles.sportCard, 
          isSelected && styles.selectedSportCard
        ]} 
        onPress={() => toggleSport(sport)}
      >
        <View style={styles.sportIconContainer}>
          <Image
                     source={{ uri: sport?.icon }} // Use sport.sport.icon instead of selectedSport
                     style={[
                       styles.sportIcon,
                       !isSelected ? { tintColor: '#EFEFEF' } : {}, // Fix isSlected typo and apply conditional style correctly
                     ]}
                     resizeMode="contain"
                   />
        </View>
        <View style={styles.sportDetails}>
          <Text style={styles.sportName}>{sport.name}</Text>
          <Text style={styles.sportDescription}>
            {sport.description.length > 20 
              ? `${sport.description.substring(0, 20)}...` 
              : sport.description}
          </Text>
          <View style={styles.sportMeta}>
            <View style={styles.metaItem}>
              <Users color={Colors.lightGray} size={16} />
              <Text style={styles.metaText}>All ages</Text>
            </View>
            <View style={styles.metaItem}>
              <Clock color={Colors.lightGray} size={16} />
              <Text style={styles.metaText}>Flexible timing</Text>
            </View>
          </View>
        </View>
        <ChevronRight color={isSelected ? Colors.lime : Colors.lightGray} size={24} />
      </TouchableOpacity>
    );
  };

  const handleContinue = () => {
    if (selectedSports.length > 0) {
      router.push('/trainer_timings');
    }
  };

  if (loading) {
    return (
      <LinearGradient 
        colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
        style={[styles.container, styles.center]}
      >
        <ActivityIndicator size="large" color={Colors.lime} />
      </LinearGradient>
    );
  }

  if (error) {
    return (
      <LinearGradient 
        colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
        style={[styles.container, styles.center]}
      >
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity 
          style={styles.retryButton}
          onPress={() => router.replace('/trainer_home')}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}
    >
      <SafeAreaView style={styles.container}>
        {/* Header with gradient */}
        <LinearGradient 
          colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)", Colors.lime]}
          style={styles.gradient} 
          start={{x: 0.5, y: 0}}
          end={{x: 0.5, y: 1}}
        />
         
        <View style={styles.header}>
          <ChevronLeft color={Colors.lime} size={24} onPress={() => router.back()} />
          <View style={{padding: 12}}>
            <Text style={styles.username}>Trainer Profile</Text>
            <View style={styles.headerTitleContainer}>
              <Text style={styles.societyName}>Select your sports</Text>
            </View>
          </View>
          <TouchableOpacity onPress={() => router.push('/tabs/more?userType=trainer')}>
            <CircleUser
              color={Colors.lime}
              size={40}
              strokeWidth={1}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.sportSelectionContainer}>
          <Text style={styles.sectionTitle}>Select Sports</Text>
          <Text style={styles.sectionSubtitle}>
            {selectedSports.length > 0 
              ? `${selectedSports.length} selected` 
              : 'Select at least one to continue'}
          </Text>

          <ScrollView style={styles.sportsList}>
            {allSports.map(renderSportItem)}
          </ScrollView>
        </View>

        <View style={styles.continueContainer}>
          <TouchableOpacity
            style={[
              styles.continueButton,
              selectedSports.length === 0 && styles.disabledButton
            ]}
            onPress={handleContinue}
            disabled={selectedSports.length === 0}
          >
            <Text style={styles.continueButtonText}>
              Continue
            </Text>
            <ArrowRight color={selectedSports.length > 0 ? Colors.black : Colors.lightGray} />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    marginBottom: 16,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 4,
  },
  societyName: {
    fontSize: 14,
    color: Colors.white,
    fontFamily: Fonts.iLight,
  },
  username: {
    fontSize: 20,
    color: Colors.white,
    fontFamily: Fonts.iMedium,
  },
  sportSelectionContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.lightGray,
    marginTop: 0,
    fontFamily: Fonts.iLight,
    marginBottom: 16,
  },
  sportsList: {
    flex: 1,
    gap: 12,
  },
  sportCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.darkBg,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  selectedSportCard: {
    borderColor: Colors.lime,
    borderWidth: 1,
  },
  sportIconContainer: {
    marginRight: 16,
    borderRadius: 8,
    padding: 8,
  },
  sportIcon: {
    width: 60,
    height: 60,
  },
  sportDetails: {
    flex: 1,
  },
  sportName: {
    fontSize: 16,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 4,
  },
  sportDescription: {
    fontSize: 14,
    color: Colors.white,
    fontFamily: Fonts.iLight,
    marginBottom: 8,
  },
  sportMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: Colors.white,
    fontFamily: Fonts.iLight,
  },
  continueContainer: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  continueButton: {
    flexDirection: 'row',
    backgroundColor: Colors.lime,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  disabledButton: {
    backgroundColor: Colors.gray,
  },
  continueButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontFamily: Fonts.iSemiBold,
  },
  errorText: {
    color: Colors.white,
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.lime,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
  },
});

export default TrainerHomes;