import {
    StyleSheet,
    Text,
    View,
    SafeAreaView,
    StatusBar,
    Image,
    FlatList,
    TouchableOpacity,
    Dimensions,
  } from 'react-native';
  import React, { useState, useRef } from 'react';
  import { Fonts } from '../styles/font';
  import { Colors } from '../styles/color';

  import { assets } from '../assets/assets';
  import useSocietyStore from '../store/society';
import { useRouter } from 'expo-router';
  
  const { width, height } = Dimensions.get('window');
  
  const slides = [
    {
      id: '1',
      title: 'Get Trained in',
      image: assets.c1,
      subtitle: '"Prestige High Fields"',
      description: 'Get Trained in "Prestige High Fields"',
    },
    {
      id: '2',
      title: 'Ease of choosing from',
      image: assets.c2,
      subtitle: 'Highly Rated Trainers',
      description: 'Ease of choosing from Highly Rated Trainers',
    },
    {
      id: '3',
      title: 'Notifications & Progress',
      image: assets.c3,
      // subtitle: 'Trackers',
      description: 'Notifications & Progress',
    },
    {
      id: '4',
      title: 'Structure & Exposure of sport',
      image: assets.c4,
      subtitle: 'with Right Trainers',
      description: 'Structure & Exposure of sport with right Trainer',
    },
  
  ];
  
  const CarouselItem = ({ item, width }: any) => {
 // Determine which image to use based on the item's image property
 const imageSource = item.image === assets.c1 ? assets.c1 :
 item.image === assets.c2 ? assets.c2 :
 item.image === assets.c3 ? assets.c3 :
 item.image === assets.c4 ? assets.c4 : assets.c1;    
    return (
      <View style={[styles.slide, { width }]}>
        <View style={styles.slideContent}>
          <View style={styles.iconContainer}>
           <Image source={imageSource} style={{width: 100, height: 100}} />
          </View>
          <Text style={styles.slideTitle}>{item.title}</Text>
          <Text style={styles.slideSubtitle}>{item.subtitle}</Text>
        </View>
      </View>
    );
  };
  
  const Pagination = ({ data, activeIndex }: any) => {
    return (
      <View style={styles.pagination}>
        {data.map((_: any, i: any) => (
          <View
            key={i}
            style={[
              styles.paginationDot,
              activeIndex === i ? styles.paginationDotActive : styles.paginationDotInactive,
            ]}
          />
        ))}
      </View>
    );
  };
  
  const Carousel = () => {
    const router = useRouter();
    const [currentIndex, setCurrentIndex] = useState(0);
    const flatListRef = useRef<FlatList>(null);
    const selectedSociety = useSocietyStore((state: any) => state.selectedSociety);
  
    const updatedSlides = [...slides];
    if (selectedSociety) {
      updatedSlides[0].subtitle = `"${selectedSociety.name}"`;
      updatedSlides[0].description = `Get Trained in "${selectedSociety.name}"`;
    }
  
    const handleScroll = (event: any) => {
      const scrollPosition = event.nativeEvent.contentOffset.x;
      const index = Math.round(scrollPosition / width);
      setCurrentIndex(index);
    };
  
    const handleSkip = () => {
      const lastIndex = updatedSlides.length - 1;
      flatListRef.current?.scrollToIndex({
        index: lastIndex,
        animated: true,
      });
      setCurrentIndex(lastIndex);
    };
  
    const handleNext = () => {
      if (currentIndex < updatedSlides.length - 1) {
        const nextIndex = currentIndex + 1;
        flatListRef.current?.scrollToIndex({
          index: nextIndex,
          animated: true,
        });
        setCurrentIndex(nextIndex);
      }
    };
  
    const handleContinue = () => {
      router.push('/tabs');
    };
  
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="rgba(0, 0, 0, 0.9)" />
        
        <View style={styles.headerContainer}>
          <Image 
            source={assets.icon} 
            style={styles.logo} 
            resizeMode="contain"
          />
        
        </View>
        
        <View style={styles.carouselTitleContainer}>
          <Text style={styles.carouselTitle}>Why Step-Up ?</Text>
        </View>
        
        <FlatList
          ref={flatListRef}
          data={updatedSlides}
          renderItem={({ item }) => <CarouselItem item={item} width={width} />}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          getItemLayout={(data, index) => ({
            length: width,
            offset: width * index,
            index,
          })}
        />
        
        <View style={styles.paginationContainer}>
          <Pagination data={updatedSlides} activeIndex={currentIndex} />
        </View>
        
        <View style={styles.buttonsContainer}>
          <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
            <Text style={styles.skipButtonText}>Skip</Text>
          </TouchableOpacity>
          
          {currentIndex === updatedSlides.length - 1 ? (
            <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
              <Text style={styles.continueButtonText}>Continue</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.continueButton} onPress={handleNext}>
              <Text style={styles.continueButtonText}>Next</Text>
            </TouchableOpacity>
          )}
        </View>
        
        <Text style={styles.termsText}>
          By registering you agree to our Terms of Service and Privacy
        </Text>
      </SafeAreaView>
    );
  };
  
  export default Carousel;
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.95)',
    },
    headerContainer: {
      alignItems: 'center',
      padding: 20,
    },
    logo: {
      width: 200,
      height: 200,
    },
    appName: {
      fontSize: 28,
      fontFamily: Fonts.iSemiBold,
      color: Colors.white,
      marginTop: 8,
    },
    highlightText: {
      color: Colors.lime,
    },
    tagline: {
      fontSize: 12,
      fontFamily: Fonts.iRegular,
      color: Colors.white,
      marginTop: 5,
    },
    carouselTitleContainer: {
      alignItems: 'center',
      marginBottom: 20,
    },
    carouselTitle: {
      fontSize: 22,
      fontFamily: Fonts.iSemiBold,
      color: Colors.white,
    },
    slide: {
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    slideContent: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    iconContainer: {
      width: 120,
      height: 120,
      borderRadius: 400,
      
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    slideTitle: {
      textAlign: 'center',
      fontSize: 18,
      fontFamily: Fonts.iMedium,
      color: Colors.white,
      marginBottom: 10,
    },
    slideSubtitle: {
      textAlign: 'center',
      fontSize: 16,
      fontFamily: Fonts.iRegular,
      color: Colors.lime,
    },
    paginationContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginVertical: 20,
    },
    pagination: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    paginationDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
    },
    paginationDotInactive: {
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
    },
    paginationDotActive: {
      backgroundColor: Colors.lime,
    },
    buttonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: 24,
      marginBottom: 20,
    },
    skipButton: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderWidth: 1,
      borderColor: Colors.gray,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    skipButtonText: {
      color: Colors.white,
      fontFamily: Fonts.iMedium,
      fontSize: 16,
    },
    continueButton: {
      backgroundColor: Colors.lime,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    continueButtonText: {
      color: Colors.black,
      fontFamily: Fonts.iSemiBold,
      fontSize: 16,
    },
    termsText: {
      textAlign: 'center',
      fontSize: 12,
      fontFamily: Fonts.iRegular,
      color: 'rgba(255, 255, 255, 0.6)',
      marginTop: 10,
      marginBottom: 20,
      paddingHorizontal: 24,
    },
    gradient:{
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
        zIndex:0,
        opacity:0.1,
        height: height * 0.4
      },
      topGradient:{
        position: "absolute",
        left: 0,
        opacity:0.1,
        right: 0,
        top: 0,
        zIndex:0,
     
        height: height * 0.4
      }
  });