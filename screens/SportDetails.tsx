import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import {
  ChevronLeft,
  Users,
  Calendar,
} from 'lucide-react-native';
import {Colors} from '../styles/color';
import {Fonts} from '../styles/font';
import useSportStore from '../store/sport';
import LinearGradient from 'react-native-linear-gradient';
import { useRouter } from 'expo-router';
const height = Dimensions.get('window').height;
const SportDetails = () => {
  const router = useRouter();
  const selectedSport = useSportStore(state => state.selectedSport) as {
    name: string;
    sport: any;
    imageUrl: string;
    description: string;
    ageCategories: Record<string, {minAge: number; maxAge: number}>;
    pricing: Record<
      string,
      {oneMonth: number; threeMonths: number; sixMonths: number}
    >;
  };


  const renderPricingCard = (category: string, pricing: { oneMonth: number; threeMonths: number; sixMonths: number }) => (
    <View style={styles.pricingCard} key={category}>
      <View style={styles.pricingHeader}>
        <Text style={styles.pricingCategory}>{category}</Text>
        <Users color={Colors.white} size={20} />
      </View>
      <View style={styles.pricingDetails}>
        {[
          { label: '1 Month   ', duration: pricing.oneMonth },
          { label: '3 Months ', duration: pricing.threeMonths },
          { label: '6 Months ', duration: pricing.sixMonths }
        ].map((plan, index) => (
          <View key={index} style={styles.pricingRow}>
           
            <Text style={styles.pricingText}>
              {plan.label}: 
            </Text>
            <Text style={{ fontFamily: Fonts.iMedium,color: Colors.white }}>₹{plan.duration}/ month</Text>
            
          </View>
        ))}
      </View>
    </View>
  );


  if (!selectedSport) {
    return (
      <View style={styles.container}>
        <Text>No sport selected</Text>
      </View>
    );
  }

  return (
    <LinearGradient
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}>
      <LinearGradient
        colors={['rgba(0, 0, 0, 0.1)', 'rgba(0, 0, 0, 0.1)', Colors.lime]}
        style={styles.gradient}
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}>
          <ChevronLeft color={Colors.lime} size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{selectedSport.sport.name}</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={{position: 'relative'}}>
          <Image
            source={{uri : selectedSport.sport.imageUrl}}
            style={styles.sportImage}
            resizeMode="cover"
          />
         
        </View>

        <View style={styles.contentContainer}>
          {/* Description Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About the Sport</Text>
            <Text style={styles.description}>
              {selectedSport.sport.description}
            </Text>
          </View>

          {/* Age Categories Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Age Categories</Text>
            <View style={styles.ageCategoriesContainer}>
              {Object.entries(selectedSport.ageCategories).map(
                ([category, ages]) => (
                  <View key={category} style={styles.ageCategoryItem}>
                    <Text style={styles.ageCategoryText}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </Text>
                    <Text style={styles.ageRangeText}>
                      {ages.minAge} - {ages.maxAge} years
                    </Text>
                  </View>
                ),
              )}
            </View>
          </View>

          {/* Pricing Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pricing Plans</Text>
            <View style={styles.pricingContainer}>
              {Object.entries(selectedSport.pricing).map(
                ([category, pricing]) =>
                  renderPricingCard(
                    category.charAt(0).toUpperCase() + category.slice(1),
                    pricing,
                  ),
              )}
            </View>
          </View>
        </View>
        <TouchableOpacity
          style={styles.chooseTimmingButton}
          onPress={() => router.push('/timings')}>
          <Calendar color={Colors.black} size={20} />
          <Text style={styles.chooseTimmingButtonText}>Choose Time Slots</Text>
        </TouchableOpacity>
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  scrollContent: {
    paddingBottom: 20, // Space for the bottom button
  },
  sportImage: {
    width: '100%',
    height: 250,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  description: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
    lineHeight: 22,
  },
  sectionTitle: {
    fontFamily: Fonts.iMedium,
    fontSize: 18,
    color: Colors.white,
    marginBottom: 12,
  },
  ageCategoriesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ageCategoryItem: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.white,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  ageCategoryText: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
    marginBottom: 4,
  },
  ageRangeText: {
    fontFamily: Fonts.iRegular,
    fontSize: 12,
    color: Colors.white,
  },
  pricingContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    gap:10
  },
  pricingCard: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.white,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
  },
  pricingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  pricingCategory: {
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
  },
  pricingDetails: {},
  pricingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap:10,
    marginBottom: 8,
  },
  pricingText: {
    fontFamily: Fonts.iLight,
    fontSize: 14,
    color: Colors.lime,
    marginLeft: 4,
  },
  chooseTimmingButton: {
    backgroundColor: Colors.lime,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    borderWidth: 1,
  },
  chooseTimmingButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
    marginLeft: 10,
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: height * 0.4,
  },
});

export default SportDetails;
