import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Dimensions,
  Modal
} from 'react-native';
import {
  ChevronLeft,
  PlusCircle,
  Trash2,
  Users,
  Calendar,
  DollarSign,
  X,
  Edit2,
  Plus
} from 'lucide-react-native';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import useSportStore from '../store/sport';
import LinearGradient from 'react-native-linear-gradient';
import useTraineeStore from '../store/traineeStore';
import { useRouter } from 'expo-router';

const height = Dimensions.get('window').height;
const width = Dimensions.get('window').width;

const ChildSelection = () => {
  const router = useRouter();
  const selectedSport = useSportStore((state: any) => state.selectedSport);
  const { trainees, addTrainee, removeTrainee, updateTrainee, duration, setDuration, totalAmount } = useTraineeStore();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [name, setName] = useState('');
  const [age, setAge] = useState('');
  const [gender, setGender] = useState('M');
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  if (!selectedSport) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No sport selected</Text>
      </View>
    );
  }

  const handleAddTrainee = () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter trainee name');
      return;
    }
    
    const ageNum = parseInt(age);
    if (isNaN(ageNum) || ageNum <= 5 || ageNum > 100) {
      Alert.alert('Error', 'Please enter a valid age');
      return;
    }
    
    if (isEditMode && editIndex !== null) {
      // Update existing trainee
      updateTrainee(editIndex, {
        name: name.trim(),
        age: ageNum,
        gender
      });
    } else {
      // Add new trainee
      addTrainee({
        name: name.trim(),
        age: ageNum,
        gender
      });
    }

    // Reset form and close modal
    resetFormAndCloseModal();
  };

  const resetFormAndCloseModal = () => {
    setName('');
    setAge('');
    setGender('M');
    setEditIndex(null);
    setIsEditMode(false);
    setModalVisible(false);
  };

  const getAgeCategory = (age: number) => {
    const { ageCategories } = selectedSport;
    
    if (age >= ageCategories.children.minAge && age <= ageCategories.children.maxAge) {
      return 'Children';
    } else if (age >= ageCategories.adults.minAge && age <= ageCategories.adults.maxAge) {
      return 'Adults';
    } else if (age >= ageCategories.elderly.minAge && age <= ageCategories.elderly.maxAge) {
      return 'Elderly';
    }
    return 'Unknown';
  };

  const handleContinue = () => {
    if (trainees.length === 0) {
      Alert.alert('Error', 'Please add at least one trainee');
      return;
    }
    
    // Navigate to the next screen (payment or confirmation)
    router.push('/success');
  };

  const openAddTraineeModal = () => {
    setName('');
    setAge('');
    setGender('M');
    setIsEditMode(false);
    setEditIndex(null);
    setModalVisible(true);
  };

  const openEditTraineeModal = (index: number) => {
    const trainee = trainees[index];
    setName(trainee.name);
    setAge(trainee.age.toString());
    setGender(trainee.gender);
    setEditIndex(index);
    setIsEditMode(true);
    setModalVisible(true);
  };

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}>
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)",Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <ChevronLeft color={Colors.lime} size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Add Trainees</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Duration Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Duration</Text>
          <View style={styles.durationContainer}>
            {[
              { label: '1 Month', value: 1 },
              { label: '3 Months', value: 3 },
              { label: '6 Months', value: 6 }
            ].map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.durationButton,
                  duration === option.value && styles.durationButtonActive
                ]}
                onPress={() => setDuration(option.value as 1 | 3 | 6)}
              >
                <Calendar 
                  color={duration === option.value ? Colors.black : Colors.white} 
                  size={18} 
                />
                <Text 
                  style={[
                    styles.durationButtonText,
                    duration === option.value && styles.durationButtonTextActive
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Add Trainee Button */}
        <TouchableOpacity 
          style={styles.addTraineeButton}
          onPress={openAddTraineeModal}
        >
          <PlusCircle color={Colors.black} size={20} />
          <Text style={styles.addTraineeButtonText}>Add New Trainee</Text>
        </TouchableOpacity>

        {/* Trainees List */}
        {trainees.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Added Trainees</Text>
            {trainees.map((trainee, index) => (
              <View key={index} style={styles.traineeCard}>
                <View style={styles.traineeInfo}>
                  <View style={styles.traineeNameContainer}>
                    <Users color={Colors.white} size={16} />
                    <Text style={styles.traineeName}>{trainee.name}</Text>
                  </View>
                  <View style={styles.traineeDetails}>
                    <Text style={styles.traineeDetail}>
                      Age: {trainee.age} ({getAgeCategory(trainee.age)})
                    </Text>
                    <Text style={styles.traineeDetail}>
                      Gender: {trainee.gender === 'M' ? 'Male' : 'Female'}
                    </Text>
                  </View>
                </View>
                <View style={styles.traineeActions}>
                  <TouchableOpacity 
                    style={styles.editButton}
                    onPress={() => openEditTraineeModal(index)}
                  >
                    <Edit2 color={Colors.lime} size={18} />
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={styles.deleteButton}
                    onPress={() => removeTrainee(index)}
                  >
                    <Trash2 color={Colors.red} size={18} />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* Summary */}
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Sport</Text>
            <Text style={styles.summaryValue}>{selectedSport.sport.name}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Duration</Text>
            <Text style={styles.summaryValue}>{duration} {duration === 1 ? 'Month' : 'Months'}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Trainees</Text>
            <Text style={styles.summaryValue}>{trainees.length}</Text>
          </View>
          <View style={styles.totalRow}>
            
            <Text style={styles.totalLabel}>Total Amount</Text>
            <Text style={styles.totalValue}>₹{totalAmount}/-</Text>
          </View>
        </View>
      </ScrollView>

      {/* Modal for Adding/Editing Trainee */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={resetFormAndCloseModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {isEditMode ? 'Edit Trainee' : 'Add New Trainee'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={resetFormAndCloseModal}
              >
                <X color={Colors.white} size={24} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.formContainer}>
              <View style={styles.inputRow}>
                <Text style={styles.inputLabel}>Name</Text>
                <TextInput
                  style={styles.input}
                  value={name}
                  onChangeText={setName}
                  placeholder="Enter name"
                  placeholderTextColor={Colors.gray}
                />
              </View>
              
              <View style={styles.inputRow}>
                <Text style={styles.inputLabel}>Age</Text>
                <TextInput
                  style={styles.input}
                  value={age}
                  onChangeText={setAge}
                  placeholder="Enter age"
                  placeholderTextColor={Colors.gray}
                  keyboardType="numeric"
                />
              </View>
              
              <View style={styles.inputRow}>
                <Text style={styles.inputLabel}>Gender</Text>
                <View style={styles.genderContainer}>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      gender === 'M' && styles.genderButtonActive
                    ]}
                    onPress={() => setGender('M')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      gender === 'M' && styles.genderButtonTextActive
                    ]}>Male</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      gender === 'F' && styles.genderButtonActive
                    ]}
                    onPress={() => setGender('F')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      gender === 'F' && styles.genderButtonTextActive
                    ]}>Female</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={resetFormAndCloseModal}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.addButton}
                onPress={handleAddTrainee}
              >
                {isEditMode ? (
                  <Edit2 color={Colors.black} size={18} />
                ) : (
                  <Plus color={Colors.black} size={18} />
                )}
                <Text style={styles.addButtonText}>
                  {isEditMode ? 'Update' : 'Add Trainee'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <TouchableOpacity 
        style={styles.continueButton}
        onPress={handleContinue}
      >
        <Text style={styles.continueButtonText}>Continue</Text>
      </TouchableOpacity>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontFamily: Fonts.iMedium,
    fontSize: 18,
    color: Colors.white,
    marginBottom: 12,
  },
  durationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  durationButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  durationButtonActive: {
    backgroundColor: Colors.lime,
    borderColor: Colors.lime,
  },
  durationButtonText: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
    marginLeft: 6,
  },
  durationButtonTextActive: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
  },
  addTraineeButton: {
    backgroundColor: Colors.lime,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    marginBottom: 24,
  },
  addTraineeButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
    marginLeft: 8,
  },
  formContainer: {
    marginBottom: 16,
  },
  inputRow: {
    marginBottom: 16,
  },
  inputLabel: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
    marginBottom: 6,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
  },
  genderContainer: {
    flexDirection: 'row',
  },
  genderButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    marginRight: 8,
    borderRadius: 8,
  },
  genderButtonActive: {
    backgroundColor: Colors.lime,
    borderColor: Colors.lime,
  },
  genderButtonText: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
  },
  genderButtonTextActive: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
  },
  traineeCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  traineeInfo: {
    flex: 1,
  },
  traineeNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  traineeName: {
    fontFamily: Fonts.iMedium,
    fontSize: 16,
    color: Colors.white,
    marginLeft: 8,
  },
  traineeDetails: {
    marginLeft: 24,
  },
  traineeDetail: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
    opacity: 0.8,
  },
  traineeActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButton: {
    padding: 8,
    marginRight: 4,
  },
  deleteButton: {
    padding: 8,
  },
  summaryContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: 24,
  },
  summaryTitle: {
    fontFamily: Fonts.iMedium,
    fontSize: 18,
    color: Colors.white,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.white,
    opacity: 0.8,
  },
  summaryValue: {
    fontFamily: Fonts.iMedium,
    fontSize: 14,
    color: Colors.white,
  },
  totalRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: 12,
    marginTop: 8,
  },
  totalLabel: {
    fontFamily: Fonts.iMedium,
    fontSize: 16,
    color: Colors.lime,
    marginLeft: 8,
    flex: 1,
  },
  totalValue: {
    fontFamily: Fonts.iSemiBold,
    fontSize: 18,
    color: Colors.lime,
  },
  continueButton: {
    backgroundColor: Colors.lime,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
  },
  continueButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
  },
  errorText: {
    fontFamily: Fonts.iMedium,
    fontSize: 16,
    color: Colors.white,
    textAlign: 'center',
    marginTop: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.bgLightGradient,
    width: width * 0.9,
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontFamily: Fonts.iMedium,
    fontSize: 18,
    color: Colors.white,
  },
  closeButton: {
    padding: 4,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  cancelButtonText: {
    fontFamily: Fonts.iMedium,
    fontSize: 14,
    color: Colors.white,
  },
  addButton: {
    flex: 1,
    backgroundColor: Colors.lime,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  addButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 14,
    marginLeft: 6,
  },
});

export default ChildSelection;