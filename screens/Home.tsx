import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import auth from "@react-native-firebase/auth";
import { showMessage } from 'react-native-flash-message';
import { useRouter } from 'expo-router';
const Home = () => {
    const router = useRouter();
    const handleLogout = async()=> {
        await auth().signOut();
        showMessage({
            message: 'Logout successful',
            type: 'success',
          });
        router.replace('/auth');
    }
  return (
    <View>
        <TouchableOpacity onPress={handleLogout}>
      <Text>Logout</Text>
      </TouchableOpacity>
    </View>
  )
}

export default Home

const styles = StyleSheet.create({})