import { StyleSheet, Text, TouchableOpacity, SafeAreaView } from 'react-native'
import React from 'react'
import auth from "@react-native-firebase/auth";
import { showMessage } from 'react-native-flash-message';
import { useRouter } from 'expo-router';
const Home = () => {
  const router = useRouter();
  const handleLogout = async () => {
    await auth().signOut();
    showMessage({
      message: 'Logout successful',
      type: 'success',
    });
    router.replace('/auth');
  }
  return (
    <SafeAreaView style={styles.container}>
      <TouchableOpacity
        onPress={handleLogout}
        accessibilityRole="button"
        accessibilityLabel="Logout Button"
        style={styles.logoutButton}
      >
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>
    </SafeAreaView>
  )
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent', // preserve parent background
  },
  logoutButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  logoutText: {
    fontSize: 16,
    color: '#ffffff',
  },
});