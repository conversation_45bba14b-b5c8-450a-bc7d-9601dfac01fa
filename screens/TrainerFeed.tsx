import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Modal,
  Dimensions,
  StatusBar,
} from 'react-native';
import { Clock, Calendar, Users, LogOut, User, X } from 'lucide-react-native';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import LinearGradient from 'react-native-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import { myTrainerBookings } from '../services/api';
import { useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

const TrainerFeed = () => {
  const router = useRouter();
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    fetchBookings();
  }, []);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      const response = await myTrainerBookings();
      setBookings(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setError('Failed to load bookings');
      setLoading(false);
    }
  };

  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const handleLogout = async () => {
    try {
      await auth().signOut();
      await AsyncStorage.removeItem("present");
      router.push('/auth');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const renderBookingItem = ({ item }: {item: any}) => {
    return (
      <View style={styles.bookingCard}>
        <View style={styles.cardHeader}>
          <View style={styles.sportBadge}>
            <Text style={styles.sportName}>{item.sportsId.name}</Text>
          </View>
          <View style={[styles.statusBadge, 
            item.status === 'confirmed' ? styles.confirmedStatus : styles.pendingStatus]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>

        <View style={styles.cardContent}>
          <View style={styles.infoRow}>
            <Calendar color={Colors.lime} size={16} />
            <Text style={styles.infoText}>
              {formatDate(item.startDate)} - {formatDate(item.endDate)}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Clock color={Colors.lime} size={16} />
            <Text style={styles.infoText}>
              {item.timeslots.map((slot: any) => 
                `${slot.startTime} - ${slot.endTime}`
              ).join(', ')}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Users color={Colors.lime} size={16} />
            <Text style={styles.infoText}>
              {item.trainees.map((trainee: any) => trainee.name).join(', ')}
            </Text>
          </View>

          <View style={styles.cardFooter}>
            <Text style={styles.priceText}>₹{item.totalAmount}</Text>
            <View style={styles.sessionDetails}>
              <Text style={styles.sessionText}>
               {item.timeslots.length} sessions @ {item.duration} Months 
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.bgDarkGradient} />
      <SafeAreaView style={styles.container}>
        <LinearGradient 
          colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)", Colors.lime]}
          style={styles.gradient} 
          start={{x: 0.5, y: 0}}
          end={{x: 0.5, y: 1}}
        />
        
        <View style={styles.header}>
          <View>
            <Text style={styles.headerTitle}>My Bookings</Text>
            <Text style={styles.headerSubtitle}>
              Manage your training sessions
            </Text>
          </View>
          <View style={{flexDirection: 'row', alignItems: 'center',gap:3}}>
          <TouchableOpacity 
            style={styles.logoutButton}
            onPress={() => router.push('/tabs/more?userType=trainer')}>

<User color={Colors.lime} size={24} />
          </TouchableOpacity>
       
          <TouchableOpacity 
            style={styles.logoutButton}
            onPress={() => setModalVisible(true)}>

            <LogOut color={Colors.lime} size={24} />
          </TouchableOpacity>
          </View>
        </View>

        {loading ? (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color={Colors.lime} />
            <Text style={styles.loaderText}>Loading bookings...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={fetchBookings}>
              <Text style={styles.retryText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : bookings.length === 0 ? (
          <View style={styles.emptyContainer}>
            <User color={Colors.lightGray} size={48} />
            <Text style={styles.emptyText}>No bookings found</Text>
            <Text style={styles.emptySubtext}>
              Your upcoming sessions will appear here
            </Text>
          </View>
        ) : (
          <FlatList
            data={bookings}
            renderItem={renderBookingItem}
            keyExtractor={item => item._id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Logout Confirmation Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}>
                <X color={Colors.white} size={24} />
              </TouchableOpacity>
              
              <Text style={styles.modalTitle}>Logout</Text>
              <Text style={styles.modalText}>
                Are you sure you want to logout from your account?
              </Text>
              
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setModalVisible(false)}>
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.modalButton, styles.confirmButton]}
                  onPress={handleLogout}>
                  <Text style={styles.confirmButtonText}>Logout</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default TrainerFeed;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
  },
  headerSubtitle: {
    fontSize: 14,
    fontFamily: Fonts.iLight,
    color: Colors.white,
    opacity: 0.8,
  },
  logoutButton: {
    padding: 8,
  },
  listContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  bookingCard: {
    backgroundColor: Colors.darkBg,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sportBadge: {
    backgroundColor: 'rgba(161, 230, 96, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 8,
  },
  sportName: {
    color: Colors.lime,
    fontFamily: Fonts.iMedium,
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 8,
  },
  confirmedStatus: {
    backgroundColor: 'rgba(34, 197, 94, 0.2)',
  },
  pendingStatus: {
    backgroundColor: 'rgba(245, 158, 11, 0.2)',
  },
  statusText: {
    fontFamily: Fonts.iMedium,
    fontSize: 12,
    color: Colors.white,
  },
  cardContent: {
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoText: {
    color: Colors.white,
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    flex: 1,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  priceText: {
    color: Colors.lime,
    fontFamily: Fonts.iSemiBold,
    fontSize: 18,
  },
  sessionDetails: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 8,
  },
  sessionText: {
    color: Colors.white,
    fontFamily: Fonts.iMedium,
    fontSize: 12,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderText: {
    marginTop: 16,
    color: Colors.white,
    fontFamily: Fonts.iRegular,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  errorText: {
    color: '#FF6B6B',
    fontFamily: Fonts.iMedium,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: Colors.lime,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: Colors.darkBg,
    fontFamily: Fonts.iSemiBold,
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  emptyText: {
    color: Colors.white,
    fontFamily: Fonts.iSemiBold,
    fontSize: 18,
    marginTop: 16,
  },
  emptySubtext: {
    color: Colors.lightGray,
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.darkBg,
    borderRadius: 16,
    padding: 24,
    width: width * 0.85,
    maxWidth: 400,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 12,
  },
  modalText: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.white,
    opacity: 0.8,
    marginBottom: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginRight: 8,
  },
  cancelButtonText: {
    color: Colors.white,
    fontFamily: Fonts.iMedium,
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: Colors.lime,
    marginLeft: 8,
  },
  confirmButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
  },
});