import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Image,
  Pressable,
  Dimensions,
  ScrollView,
} from 'react-native';
import React, { useEffect, useState, useRef } from 'react';
import auth from '@react-native-firebase/auth';
import { Fonts } from '../styles/font';
import { Colors } from '../styles/color';
import { 
  Lock, 
  RefreshCw, 
  Edit2, 
  Shield, 
  ArrowRight, 
  User, 
  Check
} from 'lucide-react-native';
import { useLoginMutation } from '../services/hooks/useLogin';
import LinearGradient from 'react-native-linear-gradient';
import { assets } from '../assets/assets';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTrainerLogin } from '../services/hooks/trainer/useTrainerLogin';
import { useRouter } from 'expo-router';
const { width, height } = Dimensions.get('window');

const Login = () => {
  const router = useRouter();
  const [confirm, setConfirm] = useState<any>(null);
  const [code, setCode] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoadingOTP, setIsLoadingOTP] = useState(false);
  const [isLoadingVerify, setIsLoadingVerify] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [otpError, setOtpError] = useState('');
  const timerRef = useRef<any>(null);
  const { mutate: login } = useLoginMutation();
  const { mutate: trainerLogin, isPending: isLoadingLogin } = useTrainerLogin();
  const [currentRole, setCurrentRole] = useState('trainer'); // Default to 'trainee'
  const loginAttemptedRef = useRef(false); // Add this ref to track login attempts
  
  // Whitelist configuration
  const WHITELISTED_PHONE = '8527179469';
  const WHITELISTED_OTP = '123456';
  
  // Responsive dimensions calculation
  const screenHeight = Dimensions.get('window').height;
  const isSmallDevice = screenHeight < 700; // Threshold for small devices

  // Handle Firebase Auth state changes
  const setRoleContext = async () => {
    await AsyncStorage.setItem("present", currentRole);
  }

  const getRoleContext = async () => {
    const role = await AsyncStorage.getItem("present");
    return role;
  }

  useEffect(() => {
    setRoleContext();
  }, [currentRole]);

  async function onAuthStateChanged(user: any) {
    if (user && user.phoneNumber && !loginAttemptedRef.current) {
      loginAttemptedRef.current = true; // Set flag to prevent multiple login attempts
      setPhoneNumber(user.phoneNumber);
      const role = await getRoleContext();
      if (role === "trainee") {
        await login({ phoneNumber: user.phoneNumber, firebaseUid: user.uid });
      } else {
        await trainerLogin({ phoneNumber: user.phoneNumber, firebaseUid: user.uid });
      }
    }
  }

  useEffect(() => {
    const subscriber = auth().onAuthStateChanged(onAuthStateChanged);
    return () => {
      subscriber();
      if (timerRef.current) clearInterval(timerRef.current);
      loginAttemptedRef.current = false; // Reset the flag when component unmounts
    };
  }, []);

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(60);
    timerRef.current = setInterval(() => {
      setResendTimer((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timerRef.current);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
  };

  // Check if phone number is whitelisted
  const isWhitelistedNumber = (phone: string) => {
    const cleanPhone = phone.replace(/\D/g, ''); // Remove non-digits
    return cleanPhone === WHITELISTED_PHONE || cleanPhone === `91${WHITELISTED_PHONE}`;
  };

  // Sign in with phone number and send OTP
  async function signInWithPhoneNumber(phone: any) {
    try {
      setIsLoadingOTP(true);
      
      // Check if it's a whitelisted number
      if (isWhitelistedNumber(phone)) {
        // For whitelisted number, create a mock confirmation object
        setConfirm({ 
          isWhitelisted: true,
          phoneNumber: phone.startsWith('+91') ? phone : `+91${phone}`
        });
        startResendTimer();
        return;
      }
      
      // Normal Firebase auth flow
      if (phone !== '' && !phone.startsWith('+91')) phone = "+91" + phone;
      const confirmation = await auth().signInWithPhoneNumber(phone);
      setConfirm(confirmation);
      startResendTimer();
    } catch (error) {
      console.log('Error sending OTP:', error);
    } finally {
      setIsLoadingOTP(false);
    }
  }

  // Confirm the OTP code entered by the user
  async function confirmCode() {
    try {
      setIsLoadingVerify(true);
      setOtpError(''); // Clear any previous errors
      
      // Check if it's a whitelisted number
      if (confirm?.isWhitelisted) {
        if (code === WHITELISTED_OTP) {
          // Simulate successful authentication for whitelisted number
          const role = await getRoleContext();
          const phoneWithCountryCode = confirm.phoneNumber;
          
          // Create a mock user object and trigger login
          if (role === "trainee") {
            await login({ 
              phoneNumber: phoneWithCountryCode, 
              firebaseUid: `whitelisted_${WHITELISTED_PHONE}` 
            });
          } else {
            await trainerLogin({ 
              phoneNumber: phoneWithCountryCode, 
              firebaseUid: `whitelisted_${WHITELISTED_PHONE}` 
            });
          }
        } else {
          setOtpError('Invalid OTP. Please try again.');
        }
        return;
      }
      
      // Normal Firebase auth flow
      await confirm.confirm(code);
      // The auth state change will trigger the login mutation
    } catch (error) {
      console.log('Invalid code.', error);
      setOtpError('Invalid OTP. Please try again.');
    } finally {
      setIsLoadingVerify(false);
    }
  }

  // Handle phone number input change
  const handlePhoneNumberChange = (text: any) => {
    setPhoneNumber(text);
  };

  // Reset to phone input screen to edit number
  const handleEditNumber = () => {
    setConfirm(null);
    setCode('');
    setOtpError('');
    if (timerRef.current) clearInterval(timerRef.current);
    setResendTimer(0);
    loginAttemptedRef.current = false; // Reset the flag
  };

  // Display global loading state when logging in after successful verification
  if (isLoadingLogin) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <StatusBar barStyle="light-content" backgroundColor={Colors.darkBg} />
        <LinearGradient 
          colors={[Colors.bgLightGradient, Colors.bgDarkGradient]} 
          style={styles.loadingBackground}
        >
          <ActivityIndicator size="large" color={Colors.lime} />
          <Text style={styles.loadingText}>Getting your account ready...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="rgba(0, 0, 0, 0.9)" />
      
      {/* Top gradient */}
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)", Colors.lime]}
        style={styles.topGradient} 
        end={{x: 0.5, y: 0}}
        start={{x: 0.5, y: 1}}
      />
      
      {/* Bottom gradient */}
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)", Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {!confirm ? (
            // Phone number input screen
            <View style={styles.card}>
              {/* Logo and Title */}
              <View style={styles.cardHeader}>
                <Image 
                  source={assets.icon} 
                  style={[
                    styles.logo,
                    isSmallDevice && styles.logoSmall
                  ]} 
                  resizeMode="contain"
                />
              </View>

              <Text style={styles.description}>
                Enter your mobile number to receive a OTP
              </Text>
              
              {/* Phone Input */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Mobile</Text>
                <View style={styles.phoneInputWrapper}>
                  <Text style={styles.countryCode}>+91</Text>
                  <TextInput
                    value={phoneNumber}
                    onChangeText={handlePhoneNumberChange}
                    placeholder="78983-54027"
                    placeholderTextColor="#666"
                    keyboardType="phone-pad"
                    style={styles.phoneInput}
                    maxLength={10}
                    editable={!isLoadingOTP}
                  />
                  {phoneNumber.length >= 10 && (
                    <View style={styles.checkmark}>
                      <Check color={Colors.lime} size={20} />
                    </View>
                  )}
                </View>
              </View>
              
              {/* Login Button */}
              <TouchableOpacity
                style={[
                  styles.loginButton,
                  (!phoneNumber || phoneNumber.length < 10) && styles.buttonDisabled
                ]}
                onPress={() => signInWithPhoneNumber(phoneNumber)}
                disabled={!phoneNumber || phoneNumber.length < 10 || isLoadingOTP}
              >
                {isLoadingOTP ? (
                  <ActivityIndicator size="small" color={Colors.black} />
                ) : (
                  <Text style={styles.loginButtonText}>LOGIN</Text>
                )}
              </TouchableOpacity>
              
              {/* People Icon and Description */}
              <View style={[styles.iconContainer, isSmallDevice && styles.iconContainerSmall]}>
                <View style={styles.peopleRow}>
                  <Image  resizeMode="contain" source={assets.kid} style={styles.peopleIcon} />
                  <Image  resizeMode="contain" source={assets.adult}  style={styles.peopleIcon} />
                  <Image resizeMode="contain" source={assets.old} style={styles.peopleIcon} />
                </View>
                <Text style={styles.trainingText}>
                  Train Children, Adults & Elders for All{'\n'}the Sports you know
                </Text>
              </View>
              
            </View>
          ) : (
            // OTP verification screen
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Shield color={Colors.lime} size={28} />
                <Text style={styles.sectionTitle}>Verify OTP</Text>
              </View>
              
              <View style={styles.phoneDisplay}>
                <Text style={styles.phoneText}>+91 {phoneNumber}</Text>
                <Pressable style={styles.editButton} onPress={handleEditNumber}>
                  <Edit2 color={Colors.white} size={16} />
                </Pressable>
              </View>
              
              <Text style={styles.description}>
                Enter the verification code sent to your phone
              </Text>
              
              <View style={styles.inputWrapper}>
                <View style={styles.otpIconContainer}>
                  <Lock color={Colors.white} size={20} />
                </View>
                <TextInput
                  value={code}
                  onChangeText={(text) => {
                    setCode(text);
                    if (otpError) setOtpError(''); // Clear error when user starts typing
                  }}
                  placeholder="Enter 6-digit OTP"
                  placeholderTextColor={Colors.white}
                  keyboardType="number-pad"
                  style={styles.input}
                  maxLength={6}
                  editable={!isLoadingVerify}
                />
              </View>
              
              {/* Error message */}
              {otpError ? (
                <Text style={styles.errorText}>{otpError}</Text>
              ) : null}
              
              {(!code || code.length < 6) ? (
                <View style={[styles.buttonGradient, styles.buttonDisabled]}>
                  <TouchableOpacity
                    style={styles.button}
                    disabled={true}
                  >
                    <Text style={[styles.buttonText, styles.buttonTextDisabled]}>Verify & Login</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <LinearGradient 
                  colors={[Colors.lime, Colors.lime]} 
                  style={styles.buttonGradient}
                >
                  <TouchableOpacity
                    style={styles.button}
                    onPress={confirmCode}
                    disabled={isLoadingVerify}
                  >
                    {isLoadingVerify ? (
                      <View style={styles.buttonLoader}>
                        <ActivityIndicator size="small" color={Colors.black} />
                        <Text style={styles.buttonText}>Verifying...</Text>
                      </View>
                    ) : (
                      <Text style={styles.buttonText}>Verify & Login</Text>
                    )}
                  </TouchableOpacity>
                </LinearGradient>
              )}
              
              <TouchableOpacity
                style={styles.resendButton}
                onPress={() => signInWithPhoneNumber(phoneNumber)}
                disabled={isLoadingOTP || isLoadingVerify || resendTimer > 0}
              >
                <View style={styles.resendContainer}>
                  <RefreshCw 
                    color={resendTimer > 0 ? Colors.gray : Colors.primary} 
                    size={16} 
                    style={styles.resendIcon}
                  />
                  {resendTimer > 0 ? (
                    <Text style={styles.resendTextDisabled}>
                      Resend OTP in {resendTimer}s
                    </Text>
                  ) : isLoadingOTP ? (
                    <ActivityIndicator size="small" color={Colors.black} />
                  ) : (
                    <Text style={styles.resendText}>Resend OTP</Text>
                  )}
                </View>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingBackground: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 18,
    fontFamily: Fonts.iMedium,
    color: Colors.lime,
  },
  card: {
    backgroundColor: "transparent",
    borderRadius: 16,
    alignItems: "center",
    padding: 16,
    width: '100%',
  },
  cardHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  logo: {
    width: 180,
    height: 180,
    marginBottom: 8,
  },
  logoSmall: {
    width: 140,
    height: 140,
    marginBottom: 4,
  },
  appTitle: {
    fontSize: 34,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 5,
  },
  tagline: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.white,
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 10,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: Colors.white,
    fontFamily: Fonts.iRegular,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  inputLabel: {
    color: Colors.white,
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    marginBottom: 8,
  },
  phoneInputWrapper: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  countryCode: {
    color: Colors.white,
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    paddingVertical: 12,
    paddingRight: 5,
  },
  phoneInput: {
    flex: 1,
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
    paddingVertical: 12,
  },
  checkmark: {
    paddingHorizontal: 10,
  },
  loginButton: {
    width: '100%',
    height: 56,
    backgroundColor: Colors.lime,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  buttonDisabled: {
    backgroundColor: '#aaa',
    opacity: 0.7,
  },
  loginButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontFamily: Fonts.iSemiBold,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainerSmall: {
    marginBottom: 16,
  },
  peopleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 20,
    marginBottom: 16,
  },
  peopleIcon: {
    width: 60,
    height: 60,
  },
  trainingText: {
    fontSize: 14,
    color: Colors.white,
    fontFamily: Fonts.iMedium,
    textAlign: 'center',
    lineHeight: 20,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 24,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: '#333',
  },
  dividerText: {
    color: Colors.white,
    paddingHorizontal: 15,
    fontFamily: Fonts.iMedium,
    fontSize: 14,
  },
  switchRoleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: Colors.lime,
    borderRadius: 10,
    gap: 10,
    width: "100%",
  },
  switchRoleText: {
    color: Colors.lime,
    fontSize: 16,
    fontFamily: Fonts.iMedium,
  },
  // OTP verification styles
  phoneDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.darkBg,
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
    width: '100%',
  },
  phoneText: {
    flex: 1,
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  editButton: {
    padding: 8,
  },
  inputWrapper: {
    width: "100%",
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    borderRadius: 8,
    marginBottom: 24,
    backgroundColor: Colors.darkBg,
  },
  otpIconContainer: {
    paddingHorizontal: 12,
  },
  input: {
    flex: 1,
    fontFamily: Fonts.iRegular,
    fontSize: 16,
    color: Colors.white,
    paddingVertical: 12,
  },
  buttonGradient: {
    borderRadius: 12,
    width: "100%",
    borderWidth: 1.6,
    borderColor: Colors.black,
  },
  button: {
    width: "100%",
    paddingVertical: 16,
    alignItems: 'center',
  },
  buttonLoader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
    marginLeft: 8,
  },
  buttonTextDisabled: {
    color: Colors.darkGray,
  },
  resendButton: {
    marginTop: 16,
    alignItems: 'center',
    padding: 8,
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resendIcon: {
    marginRight: 8,
  },
  resendText: {
    color: Colors.lime,
    fontFamily: Fonts.iMedium,
    fontSize: 14,
  },
  resendTextDisabled: {
    color: Colors.gray,
    fontFamily: Fonts.iMedium,
    fontSize: 14,
  },
  errorText: {
    color: '#ff4444',
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  topGradient: {
    position: "absolute",
    left: 0,
    opacity: 0.1,
    right: 0,
    top: 0,
    zIndex: 0,
    height: height * 0.4
  }
});