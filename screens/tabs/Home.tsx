import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  SafeAreaView, 
  TouchableOpacity, 
  Image, 
  Dimensions
} from 'react-native';
import { 
  ChevronRight, 
  Users, 
  Clock, 
  MapPin, 
  User, 
  CircleUser
} from 'lucide-react-native';
import useGetFeed from '../../services/hooks/parent/useGetFeed';
import useSportStore from '../../store/sport';
import { Fonts } from '../../styles/font';
import { Colors } from '../../styles/color';
import LinearGradient from 'react-native-linear-gradient';
import { assets } from '../../assets/assets';
import Sport from '../../utils/types';
import { useRouter } from 'expo-router';


const {height} = Dimensions.get('window');
const Home = () => {
  const router = useRouter();
  const { data, isLoading, isError } = useGetFeed();
  const setSport = useSportStore((state) => state.setSport);
  
  const [selectedSport, setSelectedSport] = useState<Sport | null>(null);
  const navigateToDetailScreen = () => {
    if (selectedSport) {
        router.push('/information');
    }
  };



  const handleSportSelect = (sport: Sport): void => {
    setSelectedSport(sport);
    setSport(sport);
    navigateToDetailScreen();
  };


  const renderSportItem = (sport: any) => {
    const isSelected = selectedSport?._id === sport._id;
  
    return (
      <TouchableOpacity
        key={sport._id}
        style={[
          styles.sportCard,
          isSelected && styles.selectedSportCard,
        ]}
        onPress={() => handleSportSelect(sport)}
      >
        <View style={styles.sportIconContainer}>
          <Image
            source={{ uri: sport.sport.icon }} // Use sport.sport.icon instead of selectedSport
            style={[
              styles.sportIcon,
              !isSelected ? { tintColor: '#EFEFEF' } : {}, // Fix isSlected typo and apply conditional style correctly
            ]}
            resizeMode="contain"
          />
        </View>
        <View style={styles.sportDetails}>
          <Text style={styles.sportName}>{sport.sport.name}</Text>
          <Text style={styles.sportDescription}>
            {sport.sport.description.slice(0, 20) + '...'}
          </Text>
          <View style={styles.sportMeta}>
            <View style={styles.metaItem}>
              <Users color={Colors.lightGray} size={16} />
              <Text style={styles.metaText}>
                For{' '}
                {[
                  sport.ageCategories.children.minAge !== 0 &&
                  sport.ageCategories.children.maxAge !== 0
                    ? 'Children'
                    : null,
                  sport.ageCategories.adults.minAge !== 0 &&
                  sport.ageCategories.adults.maxAge !== 0
                    ? 'Adults'
                    : null,
                  sport.ageCategories.elderly.minAge !== 0 &&
                  sport.ageCategories.elderly.maxAge !== 0
                    ? 'Elderly'
                    : null,
                ]
                  .filter(Boolean)
                  .join(' | ')}
              </Text>
            </View>
          </View>
        </View>
        <ChevronRight
          color={isSelected ? Colors.lime : Colors.lightGray}
          size={24}
        />
      </TouchableOpacity>
    );
  };
  return (
    <LinearGradient 
    colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
    style={styles.container}>
    <SafeAreaView style={styles.container}>
      {/* Improved Header */}
      <LinearGradient 
            colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)",Colors.lime]}
            style={styles.gradient} 
            start={{x: 0.5, y: 0}}
            end={{x: 0.5, y: 1}}
            />
         
      <View style={styles.header}>
        <View style={{padding:12}}>
        <Text style={styles.username}>
  Good {
    new Date().getHours() < 12 ? 'Morning' : 
    new Date().getHours() < 18 ? 'Afternoon' : 'Evening'
  }!
</Text>
          <View style={styles.headerTitleContainer}>
            <MapPin color={Colors.lime} size={20} />
            <Text style={styles.societyName}>{data?.data?.societyId?.name}</Text>
          </View>
         
        </View>
        <TouchableOpacity>
        {/* <CircleUser
          color={Colors.lime}
          size={40}
          strokeWidth={1}
        
        /> */}
        </TouchableOpacity>
      </View>

      <View style={styles.sportSelectionContainer}>
        <Text style={styles.sectionTitle}>Choose a Sport</Text>
        <Text style={styles.sectionSubtitle}>Select one to continue</Text>

        {isLoading ? (
          <Text style={styles.loadingText}>Loading sports...</Text>
        ) : isError ? (
          <Text style={styles.errorText}>Error loading sports</Text>
        ) : (
          <View style={styles.sportsList}>
            {data?.data?.societyId?.sports?.map(renderSportItem)}
          </View>
        )}
      </View>

      <View style={styles.bookingNotice}>
        <Clock color="#6B7280" size={16} />
        <Text style={styles.bookingNoticeText}>
          All bookings are subject to society rules. Please respect the restricted timings.
        </Text>
      </View>

    
    </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
 
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    marginBottom: 16,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 4,
  },
  societyName: {
    fontSize: 14,
    color: Colors.white,
    fontFamily: Fonts.iLight,
  },
  username: {
    fontSize: 20,
 
    color: Colors.white,
    fontFamily: Fonts.iMedium,
  },
  sportSelectionContainer: {
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.lightGray,
    marginTop: 0,
    fontFamily: Fonts.iLight,
    marginBottom: 16,
  },
  sportsList: {
    gap: 12,
  },
  sportCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.darkBg,
    borderRadius: 12,
    padding: 16,
  },
  selectedSportCard: {
    borderColor: Colors.lime,
    borderWidth: 1,

  },
  sportIconContainer: {
    marginRight: 16,
    borderRadius: 8,
    padding: 8,
  },
  sportIcon: {
    width: 60,
    height: 60,
  },
  sportDetails: {
    flex: 1,
  },
  sportName: {
    fontSize: 16,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 4,
  },
  sportDescription: {
    fontSize: 14,
    color: Colors.white,
    fontFamily: Fonts.iLight,
    marginBottom: 8,
  },
  sportMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: Colors.white,
    fontFamily: Fonts.iLight,
  },
  bookingNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 24,
    gap: 8,
  },
  bookingNoticeText: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
  proceedButton: {
    flexDirection: 'row',
    backgroundColor: Colors.purple,
    marginHorizontal: 16,
    marginTop: 24,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom:10,
    left: 16,
    right: 16
  },
  disabledButton: {
    backgroundColor: "#E5E5E5",
  },
  proceedButtonText: {

    color: Colors.black,
    fontSize: 16,
    fontFamily: Fonts.iSemiBold,
  },
  loadingText: {
    textAlign: 'center',
    color: '#6B7280',
    fontFamily: Fonts.iMedium,
  },
  errorText: {
    textAlign: 'center',
    color: 'red',
    fontFamily: Fonts.iMedium,
  },
  gradient:{ 
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
  
    opacity:0.1,
    height: height * 0.4}
});

export default Home;