import React, { useEffect, useState } from 'react';
import { 
StyleSheet, 
Text, 
View, 
SafeAreaView, 
FlatList, 
TouchableOpacity, 
ActivityIndicator,
Dimensions,
ScrollView
} from 'react-native';
import { 
Calendar, 
Clock, 
User, 
Users,
Check,
AlertCircle,
TimerOff,
ChevronRight,
Info
} from 'lucide-react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { myBookings } from '../../services/api';
import { Fonts } from '../../styles/font';
import { Colors } from '../../styles/color';
import { useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

// Define interfaces for better TypeScript support
interface Trainee {
_id: string;
name: string;
age: number;
gender: 'M' | 'F';
}

interface Trainer {
name: string;
_id: string;
}

interface TimeSlot {
startTime: string;
endTime: string;
}

interface Sport {
name: string;
_id: string;
}

interface Booking {
_id: string;
sportsId: Sport;
status: 'confirmed' | 'pending' | 'cancelled';
isVerified: boolean;
duration: number;
startDate: string;
endDate: string;
timeslots: TimeSlot[];
trainees: Trainee[];
trainer: Trainer | null;
totalAmount: number;
}

const Upcoming: React.FC = () => {
  const router = useRouter();
const [bookings, setBookings] = useState<Booking[]>([]);
const [loading, setLoading] = useState<boolean>(true);
const [error, setError] = useState<string | null>(null);
const [expandedCard, setExpandedCard] = useState<string | null>(null);

useEffect(() => {
fetchBookings();
}, []);

const fetchBookings = async (): Promise<void> => {
try {
  setLoading(true);
  const response = await myBookings();
  setBookings(response.data.bookings);
  // Set first booking as expanded by default
  if (response.data.bookings.length > 0) {
    setExpandedCard(response.data.bookings[0]._id);
  }
  setLoading(false);
} catch (error) {
  setError('Failed to load bookings. Please try again.');
  setLoading(false);
  console.error('Error fetching bookings:', error);
}
};

const formatDate = (dateString: string): string => {
const date = new Date(dateString);
return date.toLocaleDateString('en-US', { 
  month: 'short', 
  day: 'numeric', 
  year: 'numeric' 
});
};

const formatTime = (timeString: string): string => {
const [hours, minutes] = timeString.split(':');
const hour = parseInt(hours, 10);
const ampm = hour >= 12 ? 'PM' : 'AM';
const hour12 = hour % 12 || 12;
return `${hour12}:${minutes} ${ampm}`;
};

const getStatusColor = (status: string): string => {
switch(status) {
  case 'confirmed':
    return Colors.lime;
  case 'pending':
    return '#F59E0B'; // Amber
  default:
    return '#EF4444'; // Red
}
};

const getStatusIcon = (status: string, isVerified: boolean): React.ReactNode => {
if (status === 'confirmed' && isVerified) {
  return <Check size={16} color={Colors.lime} />;
} else if (status === 'pending') {
  return <AlertCircle size={16} color="#F59E0B" />;
} else {
  return <TimerOff size={16} color="#EF4444" />;
}
};

const getStatusText = (status: string, isVerified: boolean): string => {
if (status === 'pending') {
  return 'In Progress';
}
return status.charAt(0).toUpperCase() + status.slice(1) + 
        (isVerified && status === 'confirmed' ? ' • Verified' : '');
};

const toggleCardExpand = (id: string): void => {
setExpandedCard(expandedCard === id ? null : id);
};

const renderTraineeItem = (trainee: Trainee): React.ReactNode => (
<Animatable.View 
  key={trainee._id} 
  style={styles.traineeItem}
  animation="fadeIn"
  duration={500}
  useNativeDriver
>
  <View style={styles.traineeAvatar}>
    <Text style={styles.traineeInitial}>
      {trainee.name.charAt(0).toUpperCase()}
    </Text>
  </View>
  <View style={styles.traineeInfo}>
    <Text style={styles.traineeName}>{trainee.name}</Text>
    <View style={styles.traineeMeta}>
      <Text style={styles.traineeDetail}>Age: {trainee.age}</Text>
      <Text style={styles.traineeDetail}>Gender: {trainee.gender === 'M' ? 'Male' : 'Female'}</Text>
    </View>
  </View>
</Animatable.View>
);

const renderBookingCard = ({ item, index }: { item: Booking; index: number }): React.ReactNode => {
const isExpanded = expandedCard === item._id;

return (
  <Animatable.View 
    animation="fadeInUp" 
    delay={index * 150}
    duration={600}
    useNativeDriver
  >
    <TouchableOpacity 
      style={[styles.bookingCard, isExpanded && styles.expandedCard]}
      onPress={() => toggleCardExpand(item._id)}
      activeOpacity={0.9}
    >
      <View style={styles.cardHeader}>
        <View style={styles.sportInfo}>
          <Text style={styles.sportName}>{item.sportsId.name}</Text>
          <Animatable.View 
            style={styles.statusContainer}
            iterationCount={isExpanded ? "infinite" : 1}
            duration={1500}
            useNativeDriver
          >
            {getStatusIcon(item.status, item.isVerified)}
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
              {getStatusText(item.status, item.isVerified)}
            </Text>
          </Animatable.View>
        </View>
        <Animatable.View 
          style={[styles.durationBadge, { backgroundColor: Colors.darkBg }]}
          useNativeDriver
        >
          <Text style={styles.durationText}>{item.duration} {item.duration > 1 ? 'months' : 'month'}</Text>
        </Animatable.View>
      </View>

      <View style={styles.divider} />

      <View style={styles.cardContent}>
        {item.status === 'confirmed' ? <><View style={styles.infoRow}>
          <Calendar size={16} color={Colors.lightGray} />
          <Text style={styles.infoText}>
            {formatDate(item.startDate)} - {formatDate(item.endDate)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Clock size={16} color={Colors.lightGray} />
          <View style={styles.timeSlotsContainer}>
            {item.timeslots.map((slot, idx) => (
              <Text key={idx} style={styles.timeSlotText}>
                {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                {idx < item.timeslots.length - 1 ? ', ' : ''}
              </Text>
            ))}
          </View>
        </View></> :<><Text style={{color: Colors.lightGray, fontFamily:Fonts.iRegular, fontSize:14}}>Hang Tight! We will assign you the best trainer in next 24-28 hours. And, our team will reach out to you with more details.</Text></>}
        
        <View style={styles.infoRow}>
          <Users size={16} color={Colors.lightGray} />
          <Text style={styles.infoText}>
            {item.trainees.length} {item.trainees.length > 1 ? 'Trainees' : 'Trainee'}
          </Text>
        </View>
        
        {item.trainer && (
          <View style={styles.infoRow}>
            <User size={16} color={Colors.lightGray} />
            <Text style={styles.infoText}>Trainer Assigned</Text>
          </View>
        )}
      </View>
      
      {isExpanded && (
        <Animatable.View 
          style={styles.expandedContent}
          animation="fadeIn"
          duration={500}
          useNativeDriver
        >
          <View style={styles.sectionTitle}>
            <Text style={styles.sectionTitleText}>Trainee Details</Text>
          </View>
          
          <ScrollView 
            style={styles.traineesContainer}
            showsVerticalScrollIndicator={false}
          >
            {item.trainees.map(renderTraineeItem)}
          </ScrollView>
          
          {item.trainer && (
            <>
              <View style={styles.sectionTitle}>
                <Text style={styles.sectionTitleText}>Trainer Details</Text>
              </View>
              <View>
                
                <Text style={styles.trainerText}>Trainer name: {item.trainer.name}</Text>
              </View>
            </>
          )}
        </Animatable.View>
      )}
      
      <View style={styles.cardFooter}>
        <Animatable.Text 
          style={styles.amountText}
          duration={1000}
          delay={200}
          useNativeDriver
        >
          ₹{item.totalAmount}
        </Animatable.Text>
        <Animatable.View
          animation={isExpanded ? "rotate" : undefined}
          duration={300}
          useNativeDriver
        >
          <ChevronRight 
            size={20} 
            color={Colors.lightGray} 
            style={[styles.chevron, isExpanded && styles.chevronRotated]}
          />
        </Animatable.View>
      </View>
    </TouchableOpacity>
  </Animatable.View>
);
};

const renderEmptyState = (): React.ReactNode => (
<Animatable.View 
  style={styles.emptyContainer}
  animation="fadeIn"
  duration={1000}
  useNativeDriver
>
  <Animatable.View
    animation="pulse"
    iterationCount="infinite"
    duration={2000}
    useNativeDriver
  >
    <Calendar size={50} color={Colors.lightGray} />
  </Animatable.View>
  <Animatable.Text 
    style={styles.emptyTitle}
    animation="fadeInDown"
    delay={300}
    duration={800}
    useNativeDriver
  >
    No bookings found
  </Animatable.Text>
  <Animatable.Text 
    style={styles.emptyDescription}
    animation="fadeInDown"
    delay={500}
    duration={800}
    useNativeDriver
  >
    You don't have any upcoming bookings at the moment.
  </Animatable.Text>
  <Animatable.View
    useNativeDriver
  >
    <TouchableOpacity 
    onPress={() => router.push("/tabs")}
    style={styles.exploreButton}>
      <Text style={styles.exploreButtonText}>Explore Sports</Text>
    </TouchableOpacity>
  </Animatable.View>
</Animatable.View>
);

return (
<LinearGradient 
  colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
  style={styles.container}
>
  <SafeAreaView style={styles.container}>
    <Animatable.View 
      style={styles.header}
      animation="fadeInDown"
      duration={800}
      useNativeDriver
    >
      <Text style={styles.headerTitle}>My Bookings</Text>
      <TouchableOpacity onPress={fetchBookings}>
        <Animatable.Text 
          style={styles.refreshText}
          useNativeDriver
        >
          Refresh
        </Animatable.Text>
      </TouchableOpacity>
    </Animatable.View>

    {loading ? (
      <Animatable.View 
        style={styles.loadingContainer}
        animation="fadeIn"
        duration={500}
        useNativeDriver
      >
        <ActivityIndicator size="large" color={Colors.lime} />
        <Animatable.Text 
          style={styles.loadingText}
          animation="fadeIn"
          delay={300}
          duration={500}
          useNativeDriver
        >
          Loading your bookings...
        </Animatable.Text>
      </Animatable.View>
    ) : error ? (
      <Animatable.View 
        style={styles.errorContainer}
        animation="fadeIn"
        duration={800}
        useNativeDriver
      >
        <Animatable.View
          animation="shake"
          duration={1000}
          delay={300}
          useNativeDriver
        >
          <AlertCircle size={50} color="#EF4444" />
        </Animatable.View>
        <Animatable.Text 
          style={styles.errorText}
          animation="fadeIn" 
          delay={500}
          duration={800}
          useNativeDriver
        >
          {error}
        </Animatable.Text>
        <Animatable.View
          animation="bounceIn"
          delay={700}
          duration={1000}
          useNativeDriver
        >
          <TouchableOpacity style={styles.retryButton} onPress={fetchBookings}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </Animatable.View>
      </Animatable.View>
    ) : (
      <FlatList
        data={bookings}
        keyExtractor={item => item._id}
        renderItem={renderBookingCard}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
        refreshing={loading}
        onRefresh={fetchBookings}
      />
    )}
  </SafeAreaView>
</LinearGradient>
);
};

const styles = StyleSheet.create({
container: {
flex: 1,
},
header: {
padding: 16,
marginBottom: 8,
flexDirection: 'row',
justifyContent: 'space-between',
alignItems: 'center',
},
headerTitle: {
fontSize: 24,
fontFamily: Fonts.iMedium,
color: Colors.white,
},
refreshText: {
fontSize: 14,
fontFamily: Fonts.iMedium,
color: Colors.lime,
},
listContainer: {
paddingHorizontal: 16,
paddingBottom: 20,
},
bookingCard: {
backgroundColor: Colors.darkBg,
borderRadius: 12,
marginBottom: 16,
padding: 16,
shadowColor: '#000',
shadowOffset: { width: 0, height: 2 },
shadowOpacity: 0.1,
shadowRadius: 4,
elevation: 3,
},
expandedCard: {
paddingBottom: 8,
},
cardHeader: {
flexDirection: 'row',
justifyContent: 'space-between',
alignItems: 'center',
marginBottom: 12,
},
sportInfo: {
flex: 1,
},
sportName: {
fontSize: 18,
fontFamily: Fonts.iSemiBold,
color: Colors.white,
marginBottom: 4,
},
statusContainer: {
flexDirection: 'row',
alignItems: 'center',
gap: 4,
},
statusText: {
fontSize: 14,
fontFamily: Fonts.iMedium,
},
durationBadge: {
paddingHorizontal: 10,
paddingVertical: 4,
borderRadius: 16,
},
durationText: {
fontSize: 12,
fontFamily: Fonts.iMedium,
color: Colors.lime,
},
divider: {
height: 1,
backgroundColor: 'rgba(255, 255, 255, 0.1)',
marginBottom: 12,
},
cardContent: {
gap: 8,
},
infoRow: {
flexDirection: 'row',
alignItems: 'center',
gap: 8,
},
infoText: {
fontSize: 14,
fontFamily: Fonts.iLight,
color: Colors.white,
flex: 1,
},
timeSlotsContainer: {
flexDirection: 'row',
flexWrap: 'wrap',
flex: 1,
},
timeSlotText: {
fontSize: 14,
fontFamily: Fonts.iLight,
color: Colors.white,
},
expandedContent: {
marginTop: 12,
borderTopWidth: 1,
borderTopColor: 'rgba(255, 255, 255, 0.1)',
paddingTop: 12,
},
sectionTitle: {
marginBottom: 8,
},
sectionTitleText: {
fontSize: 15,
fontFamily: Fonts.iMedium,
color: Colors.lime,
},
traineesContainer: {
maxHeight: 150,
marginBottom: 12,
},
traineeItem: {
flexDirection: 'row',
alignItems: 'center',
paddingVertical: 8,
paddingHorizontal: 4,
},
traineeAvatar: {
width: 36,
height: 36,
borderRadius: 18,
backgroundColor: Colors.lime,
alignItems: 'center',
justifyContent: 'center',
marginRight: 12,
},
traineeInitial: {
fontSize: 16,
fontFamily: Fonts.iMedium,
color: Colors.black,
},
traineeInfo: {
flex: 1,
},
traineeName: {
fontSize: 14,
fontFamily: Fonts.iMedium,
color: Colors.white,
marginBottom: 2,
},
traineeMeta: {
flexDirection: 'row',
gap: 12,
},
traineeDetail: {
fontSize: 12,
fontFamily: Fonts.iLight,
color: Colors.lightGray,
},
trainerText: {
fontSize: 13,
fontFamily: Fonts.iLight,
color: Colors.lightGray,
flex: 1,
},
cardFooter: {
marginTop: 12,
paddingTop: 12,
borderTopWidth: 1,
borderTopColor: 'rgba(255, 255, 255, 0.1)',
flexDirection: 'row',
justifyContent: 'space-between',
alignItems: 'center',
},
amountText: {
fontSize: 16,
fontFamily: Fonts.iSemiBold,
color: Colors.lime,
},
chevron: {
transform: [{ rotate: '0deg' }],
},
chevronRotated: {
transform: [{ rotate: '90deg' }],
},
emptyContainer: {
alignItems: 'center',
justifyContent: 'center',
paddingVertical: 60,
},
emptyTitle: {
fontSize: 18,
fontFamily: Fonts.iMedium,
color: Colors.white,
marginTop: 16,
marginBottom: 8,
},
emptyDescription: {
fontSize: 14,
fontFamily: Fonts.iLight,
color: Colors.lightGray,
textAlign: 'center',
paddingHorizontal: 40,
marginBottom: 20,
},
exploreButton: {
backgroundColor: Colors.lime,
paddingHorizontal: 24,
paddingVertical: 12,
borderRadius: 8,
},
exploreButtonText: {
fontSize: 16,
fontFamily: Fonts.iMedium,
color: Colors.black,
},
loadingContainer: {
flex: 1,
alignItems: 'center',
justifyContent: 'center',
},
loadingText: {
marginTop: 16,
fontSize: 16,
fontFamily: Fonts.iLight,
color: Colors.white,
},
errorContainer: {
flex: 1,
alignItems: 'center',
justifyContent: 'center',
paddingHorizontal: 24,
},
errorText: {
marginTop: 16,
marginBottom: 24,
fontSize: 16,
fontFamily: Fonts.iLight,
color: Colors.white,
textAlign: 'center',
},
retryButton: {
backgroundColor: Colors.lime,
paddingHorizontal: 24,
paddingVertical: 12,
borderRadius: 8,
},
retryText: {
fontSize: 16,
fontFamily: Fonts.iMedium,
color: Colors.black,
}
});

export default Upcoming;