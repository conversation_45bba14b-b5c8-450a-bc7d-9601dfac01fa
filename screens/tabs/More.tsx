import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Modal,
  Linking,
  Share as RNShare,
  Platform,
} from 'react-native';
import { showMessage } from 'react-native-flash-message';
import auth from '@react-native-firebase/auth';
import {
  Settings,
  HelpCircle,
  Mail,
  LogOut,
  User,
  ChevronRight,
  X,
  Link,
  Notebook,
  Bug,
  Share,
  Home,
  AudioLines,
  MessageCircle,
} from 'lucide-react-native';
import { Colors } from '../../styles/color';
import { Fonts } from '../../styles/font';
import LinearGradient from 'react-native-linear-gradient';
import { useLocalSearchParams, useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

enum UserType {
  TRAINEE = 'trainee',
  TRAINER = 'trainer',
}

interface MoreProps {
  userType?: UserType;
}

const More: React.FC<MoreProps> = ({ userType = UserType.TRAINER }) => {
  const router = useRouter();
  const params = useLocalSearchParams();
  
  // Get user type from props or params, default to trainee
  const data = (params.userType as UserType) || userType || UserType.TRAINER;
  
  console.log('data...', data);
  
  const [modalVisible, setModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(auth().currentUser);

  const handleLogout = async () => {
    try {
      await auth().signOut();
      setModalVisible(false);
      showMessage({
        message: 'Logout successful',
        type: 'success',
      });
      router.push('/auth');
    } catch (error) {
      console.error('Error during logout:', error);
      setModalVisible(false);
      showMessage({
        message: 'Logout failed',
        type: 'danger',
      });
    }
  };

  const handleShareApp = async () => {
    try {
      const appName = 'YourAppName'; // Replace with your app name
      const message = `Check out ${appName}! `;
      const url =
        Platform.OS === 'ios'
          ? 'https://apps.apple.com/app/id0000000000' // Replace with your App Store URL
          : 'https://play.google.com/store/apps/details?id=com.yourapp.package'; // Replace with your Play Store URL

      const result = await RNShare.share({
        message: `${message}${url}`,
        url: url,
        title: `Share ${appName}`,
      });

      if (result.action === RNShare.sharedAction) {
        showMessage({
          message: 'App shared successfully!',
          type: 'success',
        });
      }
    } catch (error) {
      console.error('Error sharing app:', error);
      showMessage({
        message: 'Failed to share app',
        type: 'danger',
      });
    }
  };

  const menuItems = [
    ...(data === UserType.TRAINER
      ? [
          {
            icon: <AudioLines color={Colors.lime} size={22} />,
            title: 'Update Preferences',
            onPress: () => router.push('/trainer_areas'),
          },
        ]
      : []),
    {
      icon: <Notebook color={Colors.lime} size={22} />,
      title: 'Privacy Policy',
      onPress: () => router.push('/privacy_policy'),
    },
    {
      icon: <HelpCircle color={Colors.lime} size={22} />,
      title: 'FAQ',
      onPress: () => router.push('/faq'),
    },
    {
      icon: <MessageCircle color={Colors.lime} size={22} />,
      title: 'Message Us',
      onPress: () => Linking.openURL('https://wa.me/message/IENHDP3665AFO1'),
    },
    {
      icon: <Share color={Colors.lime} size={22} />,
      title: 'Share App',
      onPress: handleShareApp,
    },
  ];

  return (
    <LinearGradient
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor={Colors.bgDarkGradient}
      />

      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['rgba(0, 0, 0, 0.1)', 'rgba(0, 0, 0, 0.1)', Colors.lime]}
          style={styles.gradient}
          start={{ x: 0.5, y: 0 }}
          end={{ x: 0.5, y: 1 }}
        />

        <View style={styles.header}>
          <View>
            <Text style={styles.headerTitle}>More</Text>
            <Text style={styles.headerSubtitle}>
              Settings and information
            </Text>
          </View>
          <View>
            <TouchableOpacity onPress={() => router.back()}>
              <Home color={Colors.lime} />
            </TouchableOpacity>
          </View>
        </View>

        {currentUser && (
          <TouchableOpacity
            style={styles.profileSection}
            // onPress={() => router.push('/profile_settings')}
          >
            <View style={styles.profileIconContainer}>
              <User color={Colors.black} size={30} />
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>
                {currentUser.phoneNumber || 'User'}
              </Text>
              <Text style={styles.profileEmail}>
                {data === UserType.TRAINER ? 'Trainer' : 'Trainee'}
              </Text>
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.menuItem,
                index === menuItems.length - 1 && styles.menuItemLast,
              ]}
              onPress={item.onPress}
            >
              <View style={styles.menuItemLeft}>
                {item.icon}
                <Text style={styles.menuItemText}>{item.title}</Text>
              </View>
              <ChevronRight color={Colors.lightGray} size={20} />
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.footerContainer}>
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={() => setModalVisible(true)}
          >
            <LogOut color={Colors.white} size={22} />
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Version 1.0.0</Text>
        </View>

        {/* Logout Confirmation Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <X color={Colors.white} size={24} />
              </TouchableOpacity>

              <Text style={styles.modalTitle}>Logout</Text>
              <Text style={styles.modalText}>
                Are you sure you want to logout from your account?
              </Text>

              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setModalVisible(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modalButton, styles.confirmButton]}
                  onPress={handleLogout}
                >
                  <Text style={styles.confirmButtonText}>Logout</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default More;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.1,
    height: height * 0.4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    fontFamily: Fonts.iLight,
    color: Colors.white,
    opacity: 0.8,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: Colors.darkBg,
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  profileIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.lime,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: Colors.lime,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
  },
  profileEmail: {
    fontSize: 14,
    fontFamily: Fonts.iLight,
    color: Colors.lightGray,
  },
  menuContainer: {
    backgroundColor: Colors.darkBg,
    marginHorizontal: 16,
    borderRadius: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  menuItemLast: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
    marginLeft: 16,
  },
  footerContainer: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 59, 48, 0.6)',
    paddingVertical: 16,
    borderRadius: 16,
  },
  logoutText: {
    fontSize: 16,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginLeft: 8,
  },
  versionContainer: {
    position: 'absolute',
    bottom: 24,
    alignSelf: 'center',
  },
  versionText: {
    fontSize: 12,
    fontFamily: Fonts.iLight,
    color: Colors.lightGray,
    textAlign: 'center',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.darkBg,
    borderRadius: 20,
    padding: 24,
    width: width * 0.85,
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
  },
  modalTitle: {
    fontSize: 22,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 12,
  },
  modalText: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.white,
    opacity: 0.9,
    marginBottom: 24,
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.12)',
    marginRight: 8,
  },
  cancelButtonText: {
    color: Colors.white,
    fontFamily: Fonts.iMedium,
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: Colors.lime,
    marginLeft: 8,
  },
  confirmButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
  },
});