import { StyleSheet, Text, View, TouchableOpacity, Dimensions, Animated } from 'react-native'
import React, { useRef, useEffect } from 'react'
import LinearGradient from 'react-native-linear-gradient';
import { Check, Home } from 'lucide-react-native';
import * as Animatable from 'react-native-animatable';
import { Colors } from '../../styles/color';
import { Fonts } from '../../styles/font';
import { useRouter } from 'expo-router';

const { height, width } = Dimensions.get('window');

const AllSet = () => {
  const router = useRouter();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  
  useEffect(() => {
    animateContent();
  }, []);

  const animateContent = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start();
  };

  const handleHomePress = () => {
    router.push('/trainer_feed'); // Assuming 'tabs' is your home route
  };

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}
    >
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)", Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      
      <Animated.View 
        style={[
          styles.contentContainer, 
          {opacity: fadeAnim, transform: [{scale: scaleAnim}]}
        ]}
      >
        <Animatable.View 
          animation="bounceIn" 
          duration={1500} 
          style={styles.checkCircle}
        >
          <Check color={Colors.black} size={60} strokeWidth={3} />
        </Animatable.View>

        <Animatable.Text 
          animation="fadeInUp" 
          delay={300} 
          style={styles.title}
        >
          All Set!
        </Animatable.Text>

        <Animatable.Text 
          animation="fadeInUp" 
          delay={600} 
          style={styles.message}
        >
          Please wait for 24 hours while we verify your details
        </Animatable.Text>

        <Animatable.View 
          animation="fadeInUp" 
          delay={900} 
          style={styles.card}
        >
          <View style={styles.infoItem}>
            <View style={styles.iconContainer}>
              <Animatable.View animation="pulse" iterationCount="infinite" duration={2000}>
                <Check color={Colors.lime} size={20} />
              </Animatable.View>
            </View>
            <Text style={styles.infoText}>Your information has been submitted</Text>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.iconContainer}>
              <Animatable.View animation="pulse" iterationCount="infinite" duration={2000} delay={500}>
                <Check color={Colors.lime} size={20} />
              </Animatable.View>
            </View>
            <Text style={styles.infoText}>Our team is reviewing your details</Text>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.iconContainer}>
              <Animatable.View animation="pulse" iterationCount="infinite" duration={2000} delay={1000}>
                <Check color={Colors.lime} size={20} />
              </Animatable.View>
            </View>
            <Text style={styles.infoText}>You'll be notified once verified</Text>
          </View>
        </Animatable.View>

        <Animatable.View 
          animation="fadeInUp" 
          delay={1200}
          style={styles.noteContainer}
        >
          <Text style={styles.noteTitle}>What happens next?</Text>
          <Text style={styles.noteText}>
            Once your information is verified, you'll receive a notification and can start using all features of the app. This typically takes less than 24 hours.
          </Text>
        </Animatable.View>
      </Animated.View>

      <Animatable.View 
        animation="fadeInUp" 
        delay={1500}
      >
        <TouchableOpacity 
          style={styles.homeButton}
          onPress={handleHomePress}
        >
          <Home color={Colors.black} size={20} style={{marginRight: 8}} />
          <Text style={styles.homeButtonText}>Back to Home</Text>
        </TouchableOpacity>
      </Animatable.View>
    </LinearGradient>
  )
}

export default AllSet

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  contentContainer: {
    width: '90%',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  checkCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.lime,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    elevation: 10,
    shadowColor: Colors.lime,
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.5,
    shadowRadius: 10,
  },
  title: {
    fontSize: 32,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 18,
    fontFamily: Fonts.iMedium,
    color: Colors.lightGray,
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 24,
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    width: '100%',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoText: {
    flex: 1,
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  noteContainer: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderLeftWidth: 3,
    borderLeftColor: Colors.lime,
  },
  noteTitle: {
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 8,
  },
  noteText: {
    fontSize: 14,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
    lineHeight: 20,
  },
  homeButton: {
    backgroundColor: Colors.lime,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 24,
    flexDirection: 'row',
    marginTop:12,
   width:width*0.8
 
  },
  homeButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
  },
})