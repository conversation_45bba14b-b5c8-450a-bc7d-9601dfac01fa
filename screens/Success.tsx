import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Dimensions, ActivityIndicator, Animated, Modal, Image, Clipboard } from 'react-native'
import React, { useState, useRef } from 'react'
import useSportStore from '../store/sport';
import useTimingStore from '../store/time';
import useTraineeStore from '../store/traineeStore';
import LinearGradient from 'react-native-linear-gradient';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import { ChevronLeft, Check, Calendar, Clock, Users, DollarSign, Home, Copy } from 'lucide-react-native';
import { booking } from '../services/api';
import * as Animatable from 'react-native-animatable';
import { showMessage } from 'react-native-flash-message';
import { useRouter } from 'expo-router';

const { height, width } = Dimensions.get('window');

const Success = () => {
  const router = useRouter();
  const { selectedSport } = useSportStore();
  const { selectedTimeSlots } = useTimingStore() as any;
  const { trainees, duration, totalAmount } = useTraineeStore();
  
  const [loading, setLoading] = useState(false);
  const [bookingSuccess, setBookingSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  

  const formatDate = (dateString: any) => {
    const options: any = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  const createBooking = async () => {
    try {
      setLoading(true);
      const timeSlotStrings = selectedTimeSlots.map((slot: any) => slot.startTime);
      setShowConfirmModal(false);
      setTimeout(async () => {
        try {
          await booking(selectedSport._id, selectedTimeSlots, duration, trainees, totalAmount);
          setBookingSuccess(true);
          setLoading(false);
          animateContent();
          
          setTimeout(() => {
            router.push('/tabs');
          }, 5000);
          
        } catch (error) {
          console.error('Booking API failed:', error);
          setErrorMessage('Booking failed. Please try again.');
          setLoading(false);
        }
      }, 1500);
      
    } catch (error) {
      console.error('Booking failed:', error);
      setErrorMessage('Booking failed. Please try again.');
      setLoading(false);
    }
  };

  const handleConfirmBooking = () => {
    if (selectedSport && selectedTimeSlots.length > 0 && trainees.length > 0) {
      setShowPaymentModal(true); // Show payment modal first instead of confirm modal
    } else {
      setErrorMessage('Missing booking information. Please go back and complete your booking.');
    }
  };

  const handlePaymentConfirm = () => {
    setShowPaymentModal(false);
    setShowConfirmModal(true); // Show final confirmation after payment
  };

  const animateContent = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start();
  };

  const getAgeCategory = (age: any) => {
    if (!selectedSport?.ageCategories) return "Unknown";
    
    const { ageCategories } = selectedSport;
    
    if (age >= ageCategories.children?.minAge && age <= ageCategories.children?.maxAge) {
      return 'Children';
    } else if (age >= ageCategories.adults?.minAge && age <= ageCategories.adults?.maxAge) {
      return 'Adults';
    } else if (age >= ageCategories.elderly?.minAge && age <= ageCategories.elderly?.maxAge) {
      return 'Elderly';
    }
    return 'Unknown';
  };

  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={Colors.lime} />
      <Text style={styles.loadingText}>Processing your booking...</Text>
    </View>
  );

  const renderError = () => (
    <View style={styles.errorContainer}>
      <View style={[styles.checkCircle, styles.errorCircle]}>
        <Text style={styles.errorIcon}>!</Text>
      </View>
      <Text style={styles.errorTitle}>Booking Failed</Text>
      <Text style={styles.errorMessage}>{errorMessage}</Text>
      <TouchableOpacity 
        style={[styles.homeButton, {marginTop: 24}]}
        onPress={() => router.back()}
      >
        <Text style={styles.homeButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSuccess = () => (
    <Animated.View style={{marginTop: height/4 ,flex: 1, height:"100%",alignItems:"center", justifyContent:"center", flexDirection:"row",opacity: fadeAnim, transform: [{scale: scaleAnim}],}}>
      <View style={styles.successIconContainer}>
        <Animatable.View 
          animation="bounceIn" 
          duration={1500} 
          style={styles.checkCircle}
        >
          <Check color={Colors.black} size={36} strokeWidth={3} />
        </Animatable.View>
        <Animatable.Text 
          animation="fadeInUp" 
          delay={300} 
          style={styles.successTitle}
        >
          Booking Confirmation!
        </Animatable.Text>
        <Animatable.Text 
          animation="fadeInUp" 
          delay={600} 
          style={styles.successSubtitle}
        >
          Hang tight! We will assign you the best trainer in the next 24-48 hours. And, our team will reach out to you with more details.
        </Animatable.Text>
      </View>

      {/* <Animatable.View animation="fadeInUp" delay={900} style={styles.card}>
        <Text style={styles.cardTitle}>Booking Details</Text>
        
        <View style={styles.detailRow}>
          <View style={styles.iconContainer}>
            <Calendar color={Colors.lime} size={20} />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Your Start Date</Text>
            <Text style={styles.detailValue}>{formatDate(bookingDate)}</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.iconContainer}>
            <Clock color={Colors.lime} size={20} />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Time Slot</Text>
            <Text style={styles.detailValue}>
              {selectedTimeSlots.map(slot => slot.startTime).join(', ') || 'N/A'}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.iconContainer}>
            <Calendar color={Colors.lime} size={20} />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Duration:</Text>
            <Text style={styles.detailValue}>{duration} {duration === 1 ? 'Month' : 'Months'}</Text>
          </View>
        </View>
      </Animatable.View> */}

      {/* <Animatable.View animation="fadeInUp" delay={1200} style={styles.card}>
        <Text style={styles.cardTitle}>Sport Information</Text>
        <Text style={styles.sportName}>{selectedSport?.sport.name || 'N/A'}</Text>
        <Text style={styles.sportDescription}>{selectedSport?.sport.description?.slice(0, 100) || 'No description available'}{selectedSport?.description?.length > 100 ? '...' : ''}</Text>
      </Animatable.View> */}

      {/* <Animatable.View animation="fadeInUp" delay={1500} style={styles.card}>
        <Text style={styles.cardTitle}>Trainees</Text>
        {trainees.length > 0 ? (
          trainees.map((trainee, index) => (
            <View key={index} style={[styles.traineeCard, {borderLeftWidth: 3, borderLeftColor: Colors.lime}]}>
              <View style={styles.traineeInfo}>
                <View style={styles.traineeNameContainer}>
                  <Users color={Colors.lime} size={18} />
                  <Text style={styles.traineeName}>{trainee.name}</Text>
                </View>
                <View style={styles.traineeDetails}>
                  <Text style={styles.traineeDetail}>
                    Age: {trainee.age} ({getAgeCategory(trainee.age)})
                  </Text>
                  <Text style={styles.traineeDetail}>
                    Gender: {trainee.gender === 'M' ? 'Male' : 'Female'}
                  </Text>
                </View>
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.noTraineesText}>No trainees added</Text>
        )}
      </Animatable.View> */}

      {/* <Animatable.View animation="fadeInUp" delay={1800} style={styles.paymentCard}>
        <View style={styles.paymentRow}>
          <Text style={styles.paymentLabel}>Total Amount</Text>
          <Text style={styles.paymentValue}>₹{totalAmount}/-</Text>
        </View>
        <View style={styles.paymentStatus}>
          <DollarSign color={Colors.lime} size={18} />
          <Text style={styles.paymentStatusText}>Payment Successful</Text>
        </View>
      </Animatable.View> */}
    </Animated.View>
  );

  const qrCodeImage = 'https://firebasestorage.googleapis.com/v0/b/stepup-trainee.firebasestorage.app/o/WhatsApp%20Image%202025-06-02%20at%2014.24.47.jpeg?alt=media&token=427b3966-9922-4d74-95f5-ba037a8ac8c2'; // Replace with your actual QR code image URL

  const renderPaymentModal = () => (
    <Modal
      transparent={true}
      visible={showPaymentModal}
      animationType="fade"
      onRequestClose={() => setShowPaymentModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>Complete Payment</Text>
          <Text style={styles.modalText}>Please pay ₹{totalAmount} using the QR code below</Text>
          
          <View style={styles.qrContainer}>
            <Image 
              source={{ uri: qrCodeImage }}
              style={styles.qrImage}
            />
          </View>
          
          {/* Added payment link with copy option */}
          <View style={{ marginTop: 10, paddingHorizontal: 20, width: '100%' }}>
            <Text 
              style={{ 
                color: Colors.black, 
                textAlign: 'center', 
                paddingVertical: 8,
                borderWidth: 1,
                fontFamily: Fonts.iRegular,
                borderColor: '#ddd',
                borderRadius: 5,
                backgroundColor: '#f9f9f9'
              }}
              onPress={() => {
                Clipboard.setString("https://payment.link/example"); // assuming you have paymentLink and Clipboard imported
                showMessage({
                  message: 'Payment link copied to clipboard',
                  type: 'success',})
              }}
            >
              stepupupi@okhdfc
              {/* {paymentLink || 'https://payment.link/example'} */}
            </Text>
            <Text style={{ textAlign: 'center', marginTop: 10, color: '#666', fontSize: 12,fontFamily: Fonts.iRegular, }}>
              Tap to copy payment link
            </Text>
          </View>

          <View style={styles.paymentInfo}>
            <Text style={styles.paymentAmount}>Total: ₹{totalAmount}</Text>
            <Text style={styles.paymentInstruction}>Scan the QR code to pay</Text>
          </View>

          <View style={styles.modalButtons}>
            <TouchableOpacity 
              style={[styles.modalButton, styles.cancelButton]} 
              onPress={() => setShowPaymentModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.modalButton, styles.confirmButton]} 
              onPress={handlePaymentConfirm}
            >
              <Text style={styles.confirmButtonText}>Next</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );



  const renderConfirmModal = () => (
    <Modal
      transparent={true}
      visible={showConfirmModal}
      animationType="fade"
      onRequestClose={() => setShowConfirmModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>Confirm Booking</Text>
          <Text style={styles.modalText}>Are you sure you want to confirm this booking?</Text>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity 
              style={[styles.modalButton, styles.cancelButton]} 
              onPress={() => setShowConfirmModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.modalButton, styles.confirmButton]} 
              onPress={createBooking}
            >
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}
    >
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)",Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <ChevronLeft color={Colors.lime} size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Booking Confirmation</Text>
      </View>

      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {loading ? renderLoading() : (
          errorMessage ? renderError() : (
            bookingSuccess ? renderSuccess() : (
              <>
                <View style={styles.reviewContainer}>
                  <Text style={styles.reviewTitle}>Review Your Booking</Text>
                  <Text style={styles.reviewSubtitle}>Please review your booking details before confirming</Text>
                  
                  <View style={styles.card}>
                    <Text style={styles.cardTitle}>Booking Details</Text>
                    
                    <View style={styles.detailRow}>
                      <View style={styles.iconContainer}>
                        <Calendar color={Colors.lime} size={20} />
                      </View>
                      <View style={styles.detailContent}>
                        <Text style={styles.detailLabel}>Your Start Date</Text>
                        {/* <Text style={styles.detailValue}>{formatDate(bookingDate)}</Text> */}
                        <Text style={styles.detailValue}>InProgress</Text>

                      </View>
                    </View>

                    <View style={styles.detailRow}>
                      <View style={styles.iconContainer}>
                        <Clock color={Colors.lime} size={20} />
                      </View>
                      <View style={styles.detailContent}>
                        <Text style={styles.detailLabel}>Time Slot</Text>
                        {/* <Text style={styles.detailValue}>
                          {selectedTimeSlots.map(slot => slot.startTime).join(', ') || 'N/A'}
                        </Text> */}
                        <Text style={styles.detailValue}>
                         InProgress
                        </Text>
                      </View>
                    </View>

                    <View style={styles.detailRow}>
                      <View style={styles.iconContainer}>
                        <Calendar color={Colors.lime} size={20} />
                      </View>
                      <View style={styles.detailContent}>
                        <Text style={styles.detailLabel}>Duration:</Text>
                        <Text style={styles.detailValue}>{duration} {duration === 1 ? 'Month' : 'Months'}</Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.card}>
                    <Text style={styles.cardTitle}>Sport Information</Text>
                    <Text style={styles.sportName}>{selectedSport?.sport.name || 'N/A'}</Text>
                    <Text style={styles.sportDescription}>{selectedSport?.sport.description?.slice(0, 100) || 'No description available'}{selectedSport?.description?.length > 100 ? '...' : ''}</Text>
                  </View>

                  <View style={styles.card}>
                    <Text style={styles.cardTitle}>Trainees</Text>
                    {trainees.length > 0 ? (
                      trainees.map((trainee, index) => (
                        <View key={index} style={[styles.traineeCard, {borderLeftWidth: 3, borderLeftColor: Colors.lime}]}>
                          <View style={styles.traineeInfo}>
                            <View style={styles.traineeNameContainer}>
                              <Users color={Colors.lime} size={18} />
                              <Text style={styles.traineeName}>{trainee.name}</Text>
                            </View>
                            <View style={styles.traineeDetails}>
                              <Text style={styles.traineeDetail}>
                                Age: {trainee.age} ({getAgeCategory(trainee.age)})
                              </Text>
                              <Text style={styles.traineeDetail}>
                                Gender: {trainee.gender === 'M' ? 'Male' : 'Female'}
                              </Text>
                            </View>
                          </View>
                        </View>
                      ))
                    ) : (
                      <Text style={styles.noTraineesText}>No trainees added</Text>
                    )}
                  </View>

                  <View style={styles.paymentCard}>
                    <View style={styles.paymentRow}>
                      <Text style={styles.paymentLabel}>Total Amount</Text>
                      <Text style={styles.paymentValue}>₹{totalAmount}/-</Text>
                    </View>
                  </View>
                </View>
              </>
            )
          )
        )}
      </ScrollView>

      {!loading && !errorMessage && !bookingSuccess && (
        <TouchableOpacity 
          style={styles.homeButton}
          onPress={handleConfirmBooking}
        >
          <Check color={Colors.black} size={18} style={{marginRight: 8}} />
          <Text style={styles.homeButtonText}>Confirm Booking</Text>
        </TouchableOpacity>
      )}

      {renderPaymentModal()}
      {renderConfirmModal()}
    </LinearGradient>
  )
}

export default Success

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    opacity: 0.1,
    height: height * 0.4
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  backButton: {
    marginRight: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
   
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  scrollContent: {
   
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: height * 0.2,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  errorContainer: {
    alignItems: 'center',
    paddingTop: height * 0.1,
  },
  errorCircle: {
    backgroundColor: '#ff4d4f',
  },
  errorIcon: {
    fontSize: 36,
    fontFamily: Fonts.iBold,
    color: Colors.black,
  },
  errorTitle: {
    fontSize: 24,
    fontFamily: Fonts.iSemiBold,
    color: '#ff4d4f',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
    textAlign: 'center',
    paddingHorizontal: 24,
  },
  successIconContainer: {
    alignItems: 'center',
    marginVertical: 24,
    justifyContent: 'center',
    
  },
  checkCircle: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: Colors.lime,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    elevation: 5,
    shadowColor: Colors.lime,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  successTitle: {
    fontSize: 24,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 8,
  },
  successSubtitle: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
    marginBottom: 16,
    alignContent: 'center',
    textAlign: 'center',
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    paddingBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailContent: {
    flex: 1,
    justifyContent: 'center',
  },
  detailLabel: {
    fontSize: 14,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  sportName: {
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 8,
  },
  sportDescription: {
    fontSize: 14,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
    lineHeight: 20,
  },
  traineeCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
  },
  traineeInfo: {
    flex: 1,
  },
  traineeNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  traineeName: {
    fontFamily: Fonts.iMedium,
    fontSize: 16,
    color: Colors.white,
    marginLeft: 8,
  },
  traineeDetails: {
    marginLeft: 26,
  },
  traineeDetail: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.lightGray,
    marginBottom: 4,
  },
  noTraineesText: {
    fontFamily: Fonts.iRegular,
    fontSize: 14,
    color: Colors.lightGray,
    fontStyle: 'italic',
  },
  paymentCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  paymentLabel: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
  },
  paymentValue: {
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
    color: Colors.lime,
  },
  paymentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: 12,
  },
  paymentStatusText: {
    fontSize: 14,
    fontFamily: Fonts.iMedium,
    color: Colors.lime,
    marginLeft: 8,
  },
  homeButton: {
    backgroundColor: Colors.lime,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    elevation: 4,
    shadowColor: Colors.lime,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  homeButtonText: {
    color: Colors.black,
    fontFamily: Fonts.iSemiBold,
    fontSize: 16,
  },
  reviewContainer: {
    paddingVertical: 16,
  },
  reviewTitle: {
    fontSize: 24,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 8,
    textAlign: 'center',
  },
  reviewSubtitle: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
    marginBottom: 24,
    textAlign: 'center',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    
  },
  modalContainer: {
    backgroundColor: Colors.bgDarkGradient,
    borderRadius: 16,
    padding: 24,
    width: width * 0.85,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: Fonts.iSemiBold,
    color: Colors.white,
    marginBottom: 16,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 16,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
    marginBottom: 24,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginRight: 8,
  },
  confirmButton: {
    backgroundColor: Colors.lime,
    marginLeft: 8,
  },
  cancelButtonText: {
    fontFamily: Fonts.iMedium,
    fontSize: 16,
    color: Colors.white,
  },
  confirmButtonText: {
    fontFamily: Fonts.iMedium,
    fontSize: 16,
    color: Colors.black,
  },
  // QR Code styles
  qrContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  qrImage: {
    width: 200,
    height: 200,
    borderRadius: 10,
  },
  paymentInfo: {
    alignItems: 'center',
    marginBottom: 24,
    marginTop:10
  },
  paymentAmount: {
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
    color: Colors.lime,
    marginBottom: 8,
  },
  paymentInstruction: {
    fontSize: 14,
    fontFamily: Fonts.iRegular,
    color: Colors.lightGray,
  },
})