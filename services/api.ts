import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NavigationContainerRef} from '@react-navigation/native';
import AsyncStorageHelper from '../utils/async-cruds';
const BASE_URL = 'https://api.playpro.opengig.work/api';
// const BASE_URL = 'http://10.0.2.2:3000/api';
export const axiosInstance = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
});

let navigationRef: NavigationContainerRef<any>;

export const setNavigationRef = (ref: NavigationContainerRef<any>) => {
  navigationRef = ref;
};

axiosInstance.interceptors.request.use(
  async config => {
    const token = await AsyncStorage.getItem('token');
    console.log('Access Token:', token);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error),
);

axiosInstance.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;

    if (error.response?.status === 401 || error.response?.status === 403) {
      originalRequest._retry = true;
      await AsyncStorageHelper.clearAllData();

      console.log(
        'Token expired or access denied. Navigating to onboarding...',
      );

      if (navigationRef) {
        navigationRef.navigate('onboarding');
      } else {
        console.error('Navigation ref is not set');
      }
    }

    return Promise.reject(error);
  },
);

export const auth = async (phoneNumber:string,firebaseUid:string)=> {
  try {
    console.log('Logging in with phone number:', phoneNumber);
    const response = await axiosInstance.post('/auth', { phoneNumber, firebaseUid });
    return response.data;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
}


export const getSocieties = async ()=> {
  try {
    const response = await axiosInstance.get("/societies");
    return response.data
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
}


export const addParent = async (societyId:string)=> {
  try {
    const response = await axiosInstance.post("/user", {societyId});
    return response.data
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
}


export const getFeed = async()=> {
  try {
    console.log('Getting feed...');
    const response = await axiosInstance.get("/user/feed");
    console.log(response.data)
    return response.data
  } catch (error) {
    console.error('Error getting the feed:', error);
    throw error;
  }
}


export const trainerLogin = async (phoneNumber:string,firebaseUid:string)=> {
  try {
    const response = await axiosInstance.post('/auth/trainer', { phoneNumber, firebaseUid });
    return response.data;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
}


export const booking = async ( sportId:string, timeSlots:string[], duration:number, trainees:any[],totalAmount:number)=> {
  try {
    const response = await axiosInstance.post('/booking', { sportId, timeSlots, duration, trainees,totalAmount });
    return response.data;
  } catch (error:any) {
    console.log(error)
  }

}


export const myBookings = async ()=>{
  try {
    const response = await axiosInstance.get('/user/my-bookings');
    return response.data;
  } catch (error:any) {
    console.error('Error fetching bookings:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    
    // Forward more detailed error information
    throw new Error(error.response?.data?.message || 'Failed to fetch bookings');
  }
}



export const getAllSports = async ()=> {
  const response = await axiosInstance.get('/sports');
  return response.data;
}


export const addTrainerPreferences = async (name:string,sports:any,timeSlots:any,areas:any)=> {
  try {
  const response = await  axiosInstance.post('/trainer/choose-prefrences',{name,sports,timeSlots,areas});
  return response;
  } catch (error) {
    console.log(error)
  }
}

export const myTrainerBookings =async ()=> {
  try {
    const response = await axiosInstance.get('/trainer/my-bookings');
    return response.data
  } catch (error) {
    throw error
  }
}


export const getAllAreas = async()=> {
  const response = await axiosInstance("/area")
  return response.data
}