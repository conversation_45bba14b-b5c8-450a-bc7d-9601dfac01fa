import AsyncStorage from '@react-native-async-storage/async-storage';
import { useMutation } from '@tanstack/react-query';
import { router } from 'expo-router';
import { showMessage } from 'react-native-flash-message';
import { auth } from '../api';

export const useLoginMutation = () => {
  return useMutation({
    mutationFn: ({phoneNumber,firebaseUid}: {phoneNumber: string,firebaseUid:string}) => auth(phoneNumber,firebaseUid),
    onSuccess: data => {
      AsyncStorage.setItem('token', data.data.token);
      AsyncStorage.setItem("role","trainee");
     
     
     if(data.data.parent){
      router.replace('/(tabs)');
     }else{
      router.replace('/onboarding');
     }
    
    },
    onError: error => {
      const err = error as any;
      console.error(
        'Login failed:',
        err.response?.data?.message || err.message,
      );
      showMessage({
        message: 'Login failed',
        description: err.response?.data?.message || err.message,
        type: 'danger',
      });
      router.replace('/onboarding');
    },
  });
};
