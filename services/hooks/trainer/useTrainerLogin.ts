import AsyncStorage from '@react-native-async-storage/async-storage';
import { useMutation } from '@tanstack/react-query';
import { router } from 'expo-router';
import { trainerLogin } from '../../api';

export const useTrainerLogin = () => {
  return useMutation({
    mutationFn: ({phoneNumber,firebaseUid}: {phoneNumber: string,firebaseUid:string}) => {
      return trainerLogin(phoneNumber, firebaseUid);
    },
    onSuccess: data => {
      // Store token and role
      AsyncStorage.multiSet([
        ['token', data.data.token],
        ['role', 'trainer']
      ]).then(() => {
       

        // Always navigate to trainer_home after successful trainer login
       if(data.data.trainer){
        router.replace('/trainer_feed');
       }
       else{
        router.replace('/trainer_areas');
       }
      });
    },
    onError: error => {
      const err = error as any;
      console.error('Login failed:', err.response?.data?.message || err.message);
      
      router.replace('/onboarding');
    },
  });
};