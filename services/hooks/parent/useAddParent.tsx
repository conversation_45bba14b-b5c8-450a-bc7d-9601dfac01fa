import { useMutation } from "@tanstack/react-query";
import { useRouter } from "expo-router";
import { showMessage } from "react-native-flash-message";
import { addParent } from "../../api";

const useAddParent = () => {
    const router = useRouter();
    return useMutation({
        mutationFn: ({societyId}: {societyId:string}) => addParent(societyId),
        onSuccess: data => {
            showMessage({
                message: 'Login successful',
                type: 'success',
            });
             // Navigate to carousel screen
             router.replace('/carousel');
        },
        onError: error => {
            const err = error as any;
            console.error(
                'Login failed:',
                err.response?.data?.message || err.message,
            );
            showMessage({
                message: 'Login failed',
                description: err.response?.data?.message || err.message,
                type: 'danger',
            });
            router.replace('/onboarding');
        },
    })
}

export default useAddParent