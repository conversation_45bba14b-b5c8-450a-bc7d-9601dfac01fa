import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { ArrowLeft } from 'lucide-react-native'
import { useRouter } from 'expo-router'

const BackButton = () => {
  const router = useRouter()
  return (
     <TouchableOpacity
       onPress={() => router.back()}>
        <ArrowLeft style={{position: "static", top: 50, left: 40}}/></TouchableOpacity>
  )
}

export default BackButton

const styles = StyleSheet.create({})