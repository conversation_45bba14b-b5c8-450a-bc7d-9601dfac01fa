import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from "@react-native-firebase/auth";
import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { Dimensions, Image, StatusBar, StyleSheet, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { assets } from '../../assets/assets';
import { useTrainerLogin } from '../../services/hooks/trainer/useTrainerLogin';
import { useLoginMutation } from '../../services/hooks/useLogin';
import { Colors } from '../../styles/color';
import { Fonts } from '../../styles/font';

const { width, height } = Dimensions.get('window');

const SplashScreen = () => {
    const {mutate: trainerLogin} = useTrainerLogin();
    const {mutate: login} = useLoginMutation();
  
    useEffect(() => {
        const checkUserRole = async () => {
            try {
                // Check if user is logged in
                const currentUser = auth().currentUser;
                
                if (currentUser) {
                    // Check local storage for role
                    const storedRole = await AsyncStorage.getItem('role');
                    console.log(currentUser.phoneNumber);
                    
                    setTimeout(() => {
                        if (storedRole === 'trainer') {
                            trainerLogin({ phoneNumber: currentUser.phoneNumber || '', firebaseUid: currentUser.uid });
                        } else if (storedRole === 'trainee') {
                            login({ phoneNumber: currentUser.phoneNumber || '', firebaseUid: currentUser.uid });
                        } else {
                            // If role is not found, navigate to auth
                            router.replace('/auth');
                        }
                    }, 2000);
                } else {
                    // If no user is logged in, navigate to auth
                    setTimeout(() => {
                        router.replace('/auth');
                    }, 2000);
                }
            } catch (error) {
                console.error('Error checking user role:', error);
                // If any error occurs, navigate to auth
                router.replace('/auth');
            }
        };

        checkUserRole();
    }, []);

    return (
        <View style={styles.container}>
            <StatusBar barStyle="dark-content" backgroundColor="#000000" />
            <LinearGradient
                colors={["rgba(0, 0, 0, 0.9)", "rgba(0, 0, 0, 0.95)", Colors.lime]}
                style={styles.gradient}
                start={{x: 0.5, y: 0}}
                end={{x: 0.5, y: 1}}
            />
            
            <Image 
                source={assets.icon} 
                style={styles.logo} 
                resizeMode="contain"
            />
            
            
     
            
          
            
            <LinearGradient
                colors={["rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.1)", Colors.lime]}
                style={[styles.bottomGradient, { opacity: 0.1 }]} 
                start={{x: 0.5, y: 0}}
                end={{x: 0.5, y: 1}}
            />
             {/* Top gradient */}
                  <LinearGradient
                    colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)", Colors.lime]}
                    style={styles.topGradient} 
                    end={{x: 0.5, y: 0}}
                    start={{x: 0.5, y: 1}}
                  />
        </View>
    )
}

export default SplashScreen

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        zIndex: -1,
    },
    logo: {
        width: 300,
        height: 300,
        marginBottom: 20,
    },
    title: {
        fontSize: 40,
        fontFamily: Fonts.iSemiBold,
        color: Colors.lime,
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 16,
        fontFamily: Fonts.iRegular,
        color: Colors.white,
        marginBottom: 40,
        opacity: 0.8,
    },
    loader: {
        marginTop: 20,
    },
    bottomGradient: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 0,
        height: height * 0.3
    },
    topGradient: {
        position: "absolute",
        left: 0,
        opacity: 0.1,
        right: 0,
        top: 0,
        zIndex: 0,
        height: height * 0.4
      }
});