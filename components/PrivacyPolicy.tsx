import React from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  SafeAreaView, 
  ScrollView, 
  TouchableOpacity,
  StatusBar,
  Dimensions
} from 'react-native';
import { ChevronLeft } from 'lucide-react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Colors } from '../styles/color';
import { Fonts } from '../styles/font';
import { useRouter } from 'expo-router';

const { height } = Dimensions.get('window');

const PrivacyPolicy = () => {
  const router = useRouter();

  return (
    <LinearGradient 
      colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
      style={styles.container}
    >
      <StatusBar barStyle="light-content" backgroundColor={Colors.darkBg} />
      <LinearGradient 
        colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.1)", Colors.lime]}
        style={styles.gradient} 
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
      />
      
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
          >
            <ChevronLeft color={Colors.lime} size={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Privacy Policy</Text>
          <View style={styles.placeholder} />
        </View>
        
        <ScrollView style={styles.contentContainer}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Our Commitment to Privacy</Text>
            <Text style={styles.paragraph}>
              StepUp ("we", "our", or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application and services. Please read this Privacy Policy carefully.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Information We Collect</Text>
            <Text style={styles.paragraph}>
              We may collect the following types of information:
            </Text>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>
                <Text style={styles.bold}>Personal Information:</Text> Name, age, gender, contact information including phone number, and society/residence details.
              </Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>
                <Text style={styles.bold}>Children's Information:</Text> When creating profiles for children, we collect limited information including name, age, and gender.
              </Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>
                <Text style={styles.bold}>Activity Data:</Text> Information about sports activities, preferred schedules, and trainer assignments.
              </Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>
                <Text style={styles.bold}>Payment Information:</Text> We collect payment confirmation but do not store your financial details.
              </Text>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>How We Use Your Information</Text>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>To provide and maintain our services</Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>To process and manage bookings</Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>To match trainers with trainees</Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>To communicate with you about your account, bookings, and services</Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>To improve our application and services</Text>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Sharing Your Information</Text>
            <Text style={styles.paragraph}>
              We may share your information with:
            </Text>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>
                <Text style={styles.bold}>Trainers:</Text> Limited information to facilitate training sessions
              </Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>
                <Text style={styles.bold}>Society Administrators:</Text> For verification and facility management
              </Text>
            </View>
            <View style={styles.bulletPoint}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>
                <Text style={styles.bold}>Service Providers:</Text> Who help us deliver our services
              </Text>
            </View>
            <Text style={styles.paragraph}>
              We will not sell or rent your personal information to third parties for marketing purposes.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Security</Text>
            <Text style={styles.paragraph}>
              We implement appropriate security measures to protect your personal information. However, no electronic transmission or storage system is completely secure, and we cannot guarantee absolute security.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Children's Privacy</Text>
            <Text style={styles.paragraph}>
              Our service is intended for parents to manage their children's sports activities. We collect limited information about children under 13 only with parental consent and solely for the purpose of facilitating sports training.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Your Choices</Text>
            <Text style={styles.paragraph}>
              You can update, correct, or delete your account information at any time through the app. You may also contact us to request access to, correction of, or deletion of personal information.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Changes to Privacy Policy</Text>
            <Text style={styles.paragraph}>
              We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Us</Text>
            <Text style={styles.paragraph}>
              If you have any questions about this Privacy Policy, please contact us at:
            </Text>
            <Text style={styles.contactInfo}><EMAIL></Text>
          </View>
          
          <Text style={styles.lastUpdated}>Last Updated: April 7, 2025</Text>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default PrivacyPolicy;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  placeholder: {
    width: 40,
  },
  contentContainer: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: Fonts.iSemiBold,
    color: Colors.lime,
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 14,
    lineHeight: 22,
    color: Colors.white,
    fontFamily: Fonts.iLight,
    marginBottom: 8,
  },
  bulletPoint: {
    flexDirection: 'row',
    marginBottom: 6,
  },
  bullet: {
    fontSize: 14,
    color: Colors.lime,
    marginRight: 8,
    fontFamily: Fonts.iMedium,
  },
  bulletText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    color: Colors.white,
    fontFamily: Fonts.iLight,
  },
  bold: {
    fontFamily: Fonts.iMedium,
    color: Colors.white,
  },
  contactInfo: {
    fontSize: 14,
    color: Colors.lime,
    fontFamily: Fonts.iMedium,
    marginVertical: 8,
  },
  lastUpdated: {
    fontSize: 12,
    color: Colors.lightGray,
    fontFamily: Fonts.iLight,
    marginTop: 16,
    marginBottom: 32,
    textAlign: 'center',
  },
  gradient: { 
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.1,
    height: height * 0.4
  },
});