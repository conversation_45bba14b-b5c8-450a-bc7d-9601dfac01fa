import { 
    StyleSheet, 
    Text, 
    View, 
    ScrollView, 
    TouchableOpacity,
    SafeAreaView,
    Linking
  } from 'react-native'
  import React from 'react'
  import LinearGradient from 'react-native-linear-gradient'
  import { ChevronLeft } from 'lucide-react-native'
import { Colors } from '../styles/color'
import { Fonts } from '../styles/font'
import { useRouter } from 'expo-router'
  
  const FAQ = () => {
    const router = useRouter()
    const faqs = [
      {
        question: "How do I register as a Trainee (Parent/Child)?",
        answer: "To register as a trainee:\n- Download the app and log in using your mobile number & OTP.\n- Select your society from the dropdown list.\n- Choose the preferred activity and time slot.\n- Add your child's profile (Name, Age, Gender).\n- Confirm the booking and complete the payment via UPI.\n- Once the admin approves, you'll receive trainer details."
      },
      {
        question: "How can I become a Trainer in the app?",
        answer: "To enroll as a trainer:\n- Log in using your mobile number & OTP.\n- Select the sports/activities you can train in (multiple selections allowed).\n- Choose your preferred working hours (slots from 6 AM to 11 PM).\n- Once approved by the admin, you'll be assigned trainees based on matching time slots."
      },
      {
        question: "How are Trainers assigned to Trainees?",
        answer: "The admin manually assigns trainers based on:\n- Matching time slots between trainer availability and trainee preferences.\n- The sports/activities the trainer specializes in.\n- Once assigned, both trainer and trainee receive confirmation details."
      },
      {
        question: "What payment methods are accepted?",
        answer: "Currently, only UPI payments are supported. After booking a slot, you'll be shown a UPI scanner to complete the payment. The booking is confirmed only after admin approval."
      },
      {
        question: "Can I edit or cancel a booked session?",
        answer: "- For Trainees: You can edit/cancel a session before payment confirmation. After approval, contact support for changes.\n- For Trainers: Once slots are finalized, any changes must be requested via the admin."
      }
    ]
  
    return (
      <LinearGradient 
        colors={[Colors.bgLightGradient, Colors.bgDarkGradient]}
        style={styles.container}
      >
        <SafeAreaView style={styles.safeArea}>
          {/* Header with back button */}
          <View style={styles.header}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ChevronLeft color={Colors.lime} size={24} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>FAQ</Text>
            <View style={styles.rightSpacer} />
          </View>
  
          <ScrollView 
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
          >
            {faqs.map((faq, index) => (
              <View key={index} style={styles.faqCard}>
                <Text style={styles.questionText}>{faq.question}</Text>
                <Text style={styles.answerText}>{faq.answer}</Text>
              </View>
            ))}
  
            <View style={styles.contactContainer}>
              <Text style={styles.contactText}>Still have questions?</Text>
              <TouchableOpacity style={styles.contactButton} onPress={()=> Linking.openURL('https://wa.me/message/IENHDP3665AFO1')}>
                <Text style={styles.contactButtonText}>Contact Support</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </SafeAreaView>
      </LinearGradient>
    )
  }
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: 'rgba(255, 255, 255, 0.1)'
    },
    backButton: {
      padding: 8,
      marginLeft: -8
    },
    headerTitle: {
      fontSize: 20,
      fontFamily: Fonts.iSemiBold,
      color: Colors.white,
    },
    rightSpacer: {
      width: 24
    },
    scrollContainer: {
      paddingHorizontal: 16,
      paddingTop: 16,
      paddingBottom: 40,
    },
    faqCard: {
      backgroundColor: Colors.darkBg,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.1)'
    },
    questionText: {
      fontSize: 16,
      fontFamily: Fonts.iSemiBold,
      color: Colors.lime,
      marginBottom: 8
    },
    answerText: {
      fontSize: 14,
      fontFamily: Fonts.iRegular,
      color: Colors.white,
      lineHeight: 20
    },
    contactContainer: {
      marginTop: 24,
      alignItems: 'center'
    },
    contactText: {
      fontSize: 14,
      fontFamily: Fonts.iRegular,
      color: Colors.white,
      marginBottom: 12
    },
    contactButton: {
      backgroundColor: Colors.lime,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8
    },
    contactButtonText: {
      fontSize: 16,
      fontFamily: Fonts.iSemiBold,
      color: Colors.black
    }
  })
  
  export default FAQ