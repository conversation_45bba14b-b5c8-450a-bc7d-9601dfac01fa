{"expo": {"name": "trainer", "slug": "trainer", "version": "1.0.13", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "trainer", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.stepup.trainer", "googleServicesFile": "./GoogleService-Info.plist", "icon": {"image": "./assets/images/icon.png", "backgroundColor": "#1a1a1a"}, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "CFBundleURLTypes": [{"CFBundleURLName": "com.stepup.trainer", "CFBundleURLSchemes": ["trainer", "com.stepup.trainer", "com.googleusercontent.apps.1021379371450-hethpp6h7k1160gb5hl4rfum6qh1e2da"]}]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#000000"}, "edgeToEdgeEnabled": true, "package": "com.stepup.trainer", "googleServicesFile": "./google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "@react-native-firebase/app", "@react-native-firebase/auth", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-dev-client", {"launchMode": "most-recent"}], ["expo-build-properties", {"ios": {"useFrameworks": "static", "entitlementsFile": "./entitlements.plist"}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "0d8b615b-b3c8-4a05-9e44-cd83bcdc710b"}}, "owner": "<PERSON><PERSON><PERSON><PERSON>"}}