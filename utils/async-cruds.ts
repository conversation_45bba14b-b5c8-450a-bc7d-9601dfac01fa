import AsyncStorage from '@react-native-async-storage/async-storage';

const AsyncStorageHelper = {
  // Save data
  storeData: async (key: string, value: any) => {
    try {
      await AsyncStorage.setItem(key, value);
      console.log('Data saved successfully');
    } catch (error) {
      console.error('Error saving data:', error);
    }
  },

  // Retrieve data
  getData: async (key: any) => {
    try {
      const value = await AsyncStorage.getItem(key);
      return value;
    } catch (error) {
      console.error('Error reading data:', error);
    }
  },

  // Update data
  updateData: async (key: any, newData: any) => {
    try {
      const existingData = await AsyncStorage.getItem(key);
      if (existingData) {
        const parsedData = JSON.parse(existingData);
        const updatedData = {...parsedData, ...newData};
        await AsyncStorage.setItem(key, JSON.stringify(updatedData));
        console.log('Data updated successfully');
      }
    } catch (error) {
      console.error('Error updating data:', error);
    }
  },

  // Remove data
  removeData: async (key: any) => {
    try {
      await AsyncStorage.removeItem(key);
      console.log('Data removed successfully');
    } catch (error) {
      console.error('Error removing data:', error);
    }
  },

  // Clear all storage
  clearAllData: async () => {
    try {
      await AsyncStorage.clear();
      console.log('All data cleared');
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  },
};

export default AsyncStorageHelper;

// example

// // Storing data
// AsyncStorageHelper.storeData('user', { name: 'John Doe', age: 25 });

// // Retrieving data
// AsyncStorageHelper.getData('user').then(data => console.log(data));

// // Updating data
// AsyncStorageHelper.updateData('user', { age: 26 });

// // Removing data
// AsyncStorageHelper.removeData('user');

// // Clearing all data
// AsyncStorageHelper.clearAllData();
